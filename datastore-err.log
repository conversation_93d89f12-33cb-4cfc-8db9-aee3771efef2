panic: runtime error: index out of range [10] with length 1

goroutine 1 [running]:
main.main()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/bootstrap.go:83 +0x6bb
panic: runtime error: invalid memory address or nil pointer dereference
[signal SIGSEGV: segmentation violation code=0x1 addr=0x9 pc=0xc5f912]

goroutine 1 [running]:
github.com/pebbe/zmq4.setOption(0x3b9aca00?, 0x817ca0?, 0x450740?)
	/home/<USER>/go/pkg/mod/github.com/pebbe/zmq4@v1.3.0/zmq4.go:389 +0x12
github.com/pebbe/zmq4.(*Context).SetIoThreads(...)
	/home/<USER>/go/pkg/mod/github.com/pebbe/zmq4@v1.3.0/zmq4.go:426
motadatadatastore/server.initRequestEngine()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/server/server.go:542 +0x72
motadatadatastore/server.Start()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/server/server.go:251 +0x1413
main.main()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/bootstrap.go:161 +0x5c6
