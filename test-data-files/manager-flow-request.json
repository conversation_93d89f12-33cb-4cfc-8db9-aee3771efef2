{"500000-flow-10000000002017": {"filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["destination.port"], "type": 12, "volume.bytes": 1}, "500000-flow-10000000002018": {"egress.packets": 1, "egress.volume.bytes": 1, "filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["destination.port"], "ingress.packets": 1, "ingress.volume.bytes": 1, "type": 12}, "500000-flow-10000000002019": {"filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["source.ip", "destination.ip"], "type": 12, "volume.bytes": 1}, "500000-flow-10000000002020": {"egress.packets": 1, "egress.volume.bytes": 1, "filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["source.ip", "destination.ip"], "ingress.packets": 1, "ingress.volume.bytes": 1, "type": 12}, "500000-flow-10000000002021": {"filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["destination.ip"], "type": 12, "volume.bytes": 1}, "500000-flow-10000000002022": {"egress.packets": 1, "egress.volume.bytes": 1, "filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["destination.ip"], "ingress.packets": 1, "ingress.volume.bytes": 1, "type": 12, "volume.bytes": 1}, "500000-flow-10000000002023": {"filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["protocol"], "type": 12, "volume.bytes": 1}, "500000-flow-10000000002024": {"filters": {"data.filter": {}, "result.filter": {}}, "flows": 1, "indexable.columns": ["protocol"], "packets": 1, "type": 12, "volume.bytes": 1}, "500000-flow-10000000002025": {"filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["event.source"], "type": 12, "volume.bytes": 1}, "500000-flow-10000000002026": {"filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["source.if.index"], "type": 12, "volume.bytes": 1}, "500000-flow-10000000002027": {"filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["destination.if.index"], "type": 12, "volume.bytes": 1}, "500000-flow-1369898082963882": {"filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["destination.port"], "type": 12, "volume.bytes": 1}, "500000-flow-1369898082963883": {"filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["protocol"], "type": 12, "volume.bytes": 1}, "500000-flow-1369898082963884": {"filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["tcp.flags"], "packets": 1, "type": 12}, "500000-flow-1369898082963886": {"filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["destination.domain"], "type": 12, "volume.bytes": 1}, "500000-flow-1841296899881712": {"filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["source.domain"], "type": 12, "volume.bytes": 1}, "500000-flow-235902439670263": {"filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["destination.ip", "source.ip", "protocol", "source.port"], "type": 12, "volume.bytes": 1}, "500000-flow-287585494785": {"filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["application"], "type": 12, "volume.bytes": 1}, "500000-flow-287585494788": {"filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["destination.country", "destination.city", "source.city", "source.country"], "type": 12, "volume.bytes": 1}, "500000-flow-54829275650": {"filters": {"data.filter": {}, "drill.down.filter": {}, "result.filter": {}}, "indexable.columns": ["source.ip"], "type": 12, "volume.bytes": 1}, "500000-flow-54890276348": {"filters": {"data.filter": {}, "drill.down.filter": {}, "result.filter": {}}, "indexable.columns": ["source.ip"], "type": 12, "volume.bytes": 1}, "500000-flow-96435113520225": {"filters": {"data.filter": {}, "result.filter": {}}, "indexable.columns": ["destination.country", "source.country"], "type": 12, "volume.bytes": 1}, "500000-flow-96435113520226": {"filters": {"data.filter": {"filter": "include", "groups": [{"conditions": [{"operand": "source.city", "operator": "=", "value": "Ahmedabad"}], "filter": "include", "operator": "and"}], "operator": "and"}, "result.filter": {}}, "indexable.columns": ["destination.country", "source.city"], "type": 12, "volume.bytes": 1}}