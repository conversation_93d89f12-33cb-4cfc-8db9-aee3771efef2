package storage

import (
	"errors"
	"fmt"
	"golang.org/x/sys/windows"
	"motadatadatastore/codec"
	"motadatadatastore/utils"
	"os"
	"strings"
	"syscall"
	"unsafe"
)

var (
	kernel32       = syscall.NewLazyDLL("kernel32.dll")
	procLockFileEx = kernel32.NewProc("LockFileEx")
)

const (
	LockfileExclusiveLock   = 0x00000002
	LockfileFailImmediately = 0x00000001
)

type IOInterface struct {
	handle windows.Handle
}

func Flock(fileDescriptor int) error {
	ol := new(syscall.Overlapped)
	r, _, err := procLockFileEx.Call(
		uintptr(fileDescriptor),
		LockfileExclusiveLock|LockfileFailImmediately,
		0,
		1, 0,
		uintptr(unsafe.Pointer(ol)),
	)
	if r == 0 {
		return err
	}
	return nil
}

func OpenFile(name string) (*os.File, error) {

	handle, err := windows.CreateFile(
		windows.StringToUTF16Ptr(name),
		syscall.GENERIC_READ|syscall.GENERIC_WRITE,
		syscall.FILE_SHARE_READ|syscall.FILE_SHARE_WRITE,
		nil,
		syscall.OPEN_EXISTING, // since file is already created by os.Create
		syscall.FILE_ATTRIBUTE_NORMAL|syscall.FILE_FLAG_OVERLAPPED,
		0,
	)

	file := os.NewFile(uintptr(handle), name)

	return file, err
}

func OpenOrCreateFile(name string) (*os.File, error) {

	pathUTF16, err := syscall.UTF16PtrFromString(name)
	if err != nil {
		return nil, err
	}

	// SDDL string: D:(A;;GA;;;WD)
	// D:     -> DACL
	// (A;;GA;;;WD) -> Allow (A) Generic All (GA) to Everyone (WD)

	//sddl := "D:(A;;0x12019f;;;BA)(A;;0x12019f;;;BU)(A;;0x120089;;;WD)"
	//
	//sd, err := windows.SecurityDescriptorFromString(sddl)
	//if err != nil {
	//	return nil, err
	//}
	//
	//secAttrs := &windows.SecurityAttributes{
	//	Length:             uint32(unsafe.Sizeof(windows.SecurityAttributes{})),
	//	InheritHandle:      0,
	//	SecurityDescriptor: sd,
	//}

	handle, err := windows.CreateFile(
		pathUTF16,
		syscall.GENERIC_READ|syscall.GENERIC_WRITE,
		syscall.FILE_SHARE_READ|syscall.FILE_SHARE_WRITE,
		nil,
		syscall.OPEN_ALWAYS, // open if exists, create otherwise
		syscall.FILE_ATTRIBUTE_NORMAL|syscall.FILE_FLAG_OVERLAPPED,
		0,
	)
	if err != nil {
		return nil, err
	}

	return os.NewFile(uintptr(handle), name), nil
}

func AsyncWrite(blobSize int, file *os.File, bytes []byte) error {

	// Set up OVERLAPPED with offset at end of file
	overlapped := windows.Overlapped{
		Offset:     uint32(blobSize),
		OffsetHigh: uint32(blobSize >> 32),
		HEvent:     0, // Optional: you can use events if needed
	}

	var bytesWritten uint32

	err := windows.WriteFile(windows.Handle(file.Fd()), bytes, &bytesWritten, &overlapped)

	if err != nil && !errors.Is(err, windows.ERROR_IO_PENDING) {
		return err
	}

	// Wait for write to complete
	err = windows.GetOverlappedResult(windows.Handle(file.Fd()), &overlapped, &bytesWritten, true)
	if err != nil {
		return err
	}

	if bytesWritten != uint32(len(bytes)) {
		return fmt.Errorf("partial write: expected %d, wrote %d", len(bytes), bytesWritten)
	}

	return nil

}

// This function is now deprecated in favor of ExecuteIOCPRequests in iocp_windows.go
// It's kept here for backward compatibility
func executeIOURingRequests(partitionName string, ioPort *IOInterface, requests int, buffers [][]byte, errs []error, workerId int) error {
	// This is a compatibility wrapper that should not be called directly
	// The actual implementation is now in ExecuteIOCPRequests
	diskIOWorkerLogger.Warn("executeIOURingRequests is deprecated, use ExecuteIOCPRequests instead")
	return nil
}

func (partition *Partition) get(ioPort *IOInterface, keyBytes, valueBytes []byte, memoryPool *utils.MemoryPool, lookupWAL bool, worker *DiskIOWorker) (bool, []byte, error) {
	var err error

	if lookupWAL {

		if entry, ok := partition.entries[utils.GetHash64(keyBytes)]; ok {

			if entry.length > len(valueBytes) {

				return false, nil, errors.New(utils.ErrorTooLarge)
			}

			copy(valueBytes, partition.walBytes[entry.offset:entry.offset+entry.length])

			bytes := getValueBytes(valueBytes)

			if bytes == nil {

				return false, nil, errors.New(utils.ErrorCorrupted)
			}

			return true, bytes, nil

		}

	}

	offset, found, err := searchIndex(keyBytes, partition)

	if err != nil {

		return found, nil, err
	}

	if !found {

		return found, nil, err

	}

	index, offsetBytes := memoryPool.AcquireBytePool(8)

	defer memoryPool.ReleaseBytePool(index)

	writeINT64Value(int64(offset), offsetBytes)

	// We don't need the ioPort parameter anymore as we're using the IOCP pool
	valueBytes, err = read(nil, offsetBytes, valueBytes, partition, worker)

	if err != nil {

		return false, nil, err
	}

	return found, valueBytes, err

}

func read(ioPort *IOInterface, offsetBytes, bytes []byte, partition *Partition, worker *DiskIOWorker) ([]byte, error) {

	// if the target segment is mapped into the memory then use Memory mapped I/O rather than Disk I/O
	segmentHeader := offsetBytes[1]

	offset := codec.ReadINTValue(offsetBytes[2:])

	segmentType := getSegmentType(segmentHeader)

	segmentLength := getSegmentBufferLength(segmentType)

	if segmentLength > len(bytes) {

		return nil, errors.New(utils.ErrorTooLarge)
	}

	// If the segment is in memory, read from memory
	//if segment, ok := partition.writingSegments[segmentType]; ok && offsetBytes[0] == NotCompacted && segment == getSegment(segmentHeader) {
	//
	//	segmentBuffer := partition.segmentBuffers[segmentHeader]
	//
	//	if len(segmentBuffer) < offset+getSegmentBufferLength(segmentType) {
	//
	//		return nil, errors.New(utils.ErrorCorrupted)
	//	}
	//
	//	copy(bytes, segmentBuffer[offset:offset+segmentLength])
	//
	//} else {

	// Create a wait group for this single request

	// Determine which file to read from
	var fileHandle windows.Handle

	if offsetBytes[0] == NotCompacted {

		fileHandle = windows.Handle(partition.segmentFiles[segmentHeader].Fd())

	} else {

		fileHandle = windows.Handle(partition.segment255.Fd())
	}

	var context = worker.ReadRequests[0]

	// Create a read request

	context.file = fileHandle

	context.index = 0

	context.bytes = bytes[:segmentLength]

	context.workerId = worker.workerId

	context.waitGroup = worker.waitGroup

	context.overlapped.Offset = uint32(offset)

	context.overlapped.OffsetHigh = uint32(offset >> 32)

	// Add to wait group before submitting
	worker.waitGroup.Add(1)

	// Associate the file with the IOCP
	_, err := windows.CreateIoCompletionPort(fileHandle, iocpWorkers[utils.GetFastModN(utils.GetHash64([]byte(codec.INTToStringValue(int(fileHandle)))), utils.IOCPWorkers)].iocpHandle, 1, 0)

	if err != nil && !strings.Contains(err.Error(), "The parameter is incorrect.") {

		return nil, err
	}

	overlappeds.Store(&context.overlapped, context)

	diskIOWorkerLogger.Info(fmt.Sprintf("queueing single req at %p for workerId=%v", &context.overlapped, context.workerId))

	// Submit the read request
	err = windows.ReadFile(fileHandle, bytes[:segmentLength], nil, &context.overlapped)

	if err != nil && !errors.Is(err, syscall.ERROR_IO_PENDING) {

		return nil, err
	}

	// Wait for completion
	worker.waitGroup.Wait()

	// Check for errors
	if context.err != nil {

		return nil, context.err
	}
	//}

	valueBytes := getValueBytes(bytes)

	if valueBytes == nil {

		return nil, errors.New(utils.ErrorCorrupted)
	}

	return valueBytes, nil
}
