05-June-2025 05:31:58.103694 PM DEBUG [Data Aggregator]:aggregator 0 took 415.026µs to aggregate plugin view 60005-fortinet.traffic@@@1, for interval 5
05-June-2025 05:31:58.112007 PM ERROR [Data Aggregator]:error assignment to entry in nil map occurred while aggregating §§0§0
05-June-2025 05:31:58.113356 PM ERROR [Data Aggregator]:!!!STACK TRACE for data aggregator 0!!! 
 goroutine 105 [running]:
motadatadatastore/broker.(*DataAggregator).aggregate.func1()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/broker/dataaggregator.go:239 +0xf1
panic({0x842600?, 0xbbadc0?})
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/runtime/panic.go:787 +0x132
motadatadatastore/broker.(*DataAggregator).aggregate(0xc0001ac640, {0x0, 0x0, 0x0, {0x0, 0x0, 0x0}, {0x0, 0x0, 0x0}, ...})
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/broker/dataaggregator.go:259 +0x413
motadatadatastore/broker.(*DataAggregator).process(0xc0001ac640)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/broker/dataaggregator.go:204 +0x225
motadatadatastore/broker.(*DataAggregator).Start.func1()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/broker/dataaggregator.go:164 +0x5b
created by motadatadatastore/broker.(*DataAggregator).Start in goroutine 104
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/broker/dataaggregator.go:153 +0x218

05-June-2025 05:31:58.115507 PM ERROR [Data Aggregator]:error runtime error: invalid memory address or nil pointer dereference occurred in data aggregator 0
05-June-2025 05:31:58.115691 PM ERROR [Data Aggregator]:!!!STACK TRACE for data aggregator 0!!! 
 goroutine 105 [running]:
motadatadatastore/broker.(*DataAggregator).process.func1()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/broker/dataaggregator.go:184 +0x113
panic({0x8425a0?, 0xbbae10?})
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/runtime/panic.go:787 +0x132
github.com/dolthub/swiss.(*Map[...]).Clear(...)
	/home/<USER>/go/pkg/mod/github.com/dolthub/swiss@v0.2.1/map.go:239
motadatadatastore/broker.(*DataAggregator).cleanup(0xc0001ac640, {0xc0000d6180, 0x8})
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/broker/dataaggregator.go:618 +0x97
motadatadatastore/broker.(*DataAggregator).aggregate.func1()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/broker/dataaggregator.go:241 +0x19f
panic({0x842600?, 0xbbadc0?})
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/runtime/panic.go:787 +0x132
motadatadatastore/broker.(*DataAggregator).aggregate(0xc0001ac640, {0x0, 0x0, 0x0, {0x0, 0x0, 0x0}, {0x0, 0x0, 0x0}, ...})
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/broker/dataaggregator.go:259 +0x413
motadatadatastore/broker.(*DataAggregator).process(0xc0001ac640)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/broker/dataaggregator.go:204 +0x225
motadatadatastore/broker.(*DataAggregator).Start.func1()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/broker/dataaggregator.go:164 +0x5b
created by motadatadatastore/broker.(*DataAggregator).Start in goroutine 104
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/broker/dataaggregator.go:153 +0x218

05-June-2025 05:31:58.115772 PM ERROR [Data Aggregator]:data aggregator 0 restarted
05-June-2025 05:31:59.123098 PM DEBUG [Data Aggregator]:aggregator 0 took 318.614µs to aggregate plugin view 60005-fortinet.traffic@@@1, for interval 5
05-June-2025 05:31:59.124815 PM ERROR [Data Aggregator]:error open /home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/broker/datastore-events/tmp-aggregations/1749124919§60005-fortinet.traffic@@@1§12§5§0§1749124919123441684: no such file or directory occurred while writing/moving buffer for key 1749124919§60005-fortinet.traffic@@@1§12§5 in aggregator 0
05-June-2025 05:31:59.125359 PM ERROR [Data Aggregator]:error runtime error: index out of range [0] with length 0 occurred while aggregating 1749124919§60005-fortinet.traffic@@@1§12§5
05-June-2025 05:31:59.125676 PM ERROR [Data Aggregator]:!!!STACK TRACE for data aggregator 0!!! 
 goroutine 106 [running]:
motadatadatastore/broker.(*DataAggregator).aggregate.func1()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/broker/dataaggregator.go:239 +0xf1
panic({0x883980?, 0xc00001a570?})
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/runtime/panic.go:787 +0x132
motadatadatastore/broker.(*DataAggregator).aggregate(0xc0001ac6e0, {0xc0000d4bd0, 0xc0000d4c00, 0xc0000d4c30, {0xc0000aeae0, 0x2, 0x2}, {0xc0000d5020, 0x3, 0x3}, ...})
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/broker/dataaggregator.go:365 +0x1825
motadatadatastore/broker.TestDatastoreAggregatorV2(0xc0005d7180?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/broker/dataaggregator_test.go:1153 +0xe05
testing.tRunner(0xc0005d7180, 0x937010)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

