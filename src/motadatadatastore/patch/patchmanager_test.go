/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* 2025-06-04             <PERSON><PERSON><PERSON> Shah            MOTADATA-5780 Test Case Refactoring
 */

package patch

import (
	"encoding/json"
	"fmt"
	"github.com/stretchr/testify/assert"
	"motadatadatastore/cache"
	"motadatadatastore/datastore"
	"motadatadatastore/patch/patches"
	"motadatadatastore/patch/patches/patchv1_11"
	"motadatadatastore/utils"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

var (
	configBytes []byte

	patchManager *PatchManager
)

func TestMain(m *testing.M) {

	if utils.SkipBenchmarkTest() {

		return
	}

	var err error

	configBytes, err = os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir))) + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	if err != nil {

		panic(err)
	}

	utils.SystemBootSequence = utils.Datastore

	if utils.InitConfigs(utils.UpdateConfigs(configBytes, utils.MotadataMap{

		"datastore.max.pool.length":                      50000,
		"system.log.level":                               0,
		"datastore.horizontal.aggregation.timer.seconds": 5,
	})) {

		utils.PublisherNotifications = make(chan utils.MotadataMap, 1_00_000)

		patchv1_11.ProbeTimerSeconds = 2

		patchManager = NewPatchManager()

		utils.CleanUpStores()

		_ = os.MkdirAll(utils.JobDir, 0777)

		datastore.Init()

		cache.InitCacheEngine()

		m.Run()

	}

}

// BUG:- MOTADATA-649
// If Not sufficient disk space the patch process starts to rollback.
func TestPatchWithNotSufficientDiskSpace(t *testing.T) {

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	utils.InitConfigs(utils.UpdateConfigs(configBytes, utils.MotadataMap{

		"datastore.minimum.available.disk.space.threshold.percent": 0.98,
	}))

	_ = utils.Unzip(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))+utils.PathSeparator+"test-patch-files"+utils.PathSeparator+"patchv1_11.zip", utils.CurrentDir)

	assertions := assert.New(t)

	_, err := os.Stat(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions.Nil(err)

	dirs, _ := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	expectedDatastores := len(dirs)

	assertions.True(expectedDatastores > 0)

	err = patchManager.ApplyPatches("1.1.1")

	assertions.NotNil(err)

	_, err = os.Stat(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions.Nil(err)

	dirs, _ = os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	acutalDatastores := len(dirs)

	assertions.True(acutalDatastores > 0)

	assertions.Equal(expectedDatastores, acutalDatastores)

}

func TestPatchWithNoDatastores(t *testing.T) {

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = os.Remove(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	assertions := assert.New(t)

	assertions.Nil(patchManager.ApplyPatches("1.14"))

	bytes, err := utils.ReadLogFile("Patch Manager", "patch")

	assertions.Nil(err)

	assertions.True(strings.Contains(string(bytes), "no datastores found to apply patch."))

}

func TestBackupWithInsufficientDiskSpace(t *testing.T) {

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	utils.InitConfigs(utils.UpdateConfigs(configBytes, utils.MotadataMap{

		"datastore.minimum.available.disk.space.threshold.percent": 0.95,
	}))

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreVariant)

	utils.EnvironmentType = utils.DatastoreTestEnvironment

	_ = os.MkdirAll(utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir, 0755)

	assertions := assert.New(t)

	assertions.NotNil(patchManager.ApplyPatches("1.10"))

	bytes, err := utils.ReadLogFile("Patch Manager", "patch")

	assertions.Nil(err)

	assertions.True(strings.Contains(string(bytes), "failed to upgrade"))

	assertions.True(strings.Contains(string(bytes), "not enough disk space to backup the previous data stores."))

}

func TestBackupWithSufficientDiskSpace(t *testing.T) {

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	utils.InitConfigs(utils.UpdateConfigs(configBytes, utils.MotadataMap{

		"datastore.minimum.available.disk.space.threshold.percent": 0.02,
	}))

	utils.EnvironmentType = utils.DatastoreTestEnvironment

	_ = os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.Patch+utils.DatastoreDir, []byte("temp zip contents..."), 0666)

	_ = os.MkdirAll(utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir+utils.PathSeparator+"temp-store", 0755)

	_ = os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir+utils.PathSeparator+"temp-store"+utils.PathSeparator+"temp-file", []byte("temp store contents."), 0666)

	assertions := assert.New(t)

	assertions.True(patchManager.backup())

	bytes, err := utils.ReadLogFile("Patch Manager", "patch")

	assertions.Nil(err)

	assertions.True(strings.Contains(string(bytes), "disk space available for taking backup of existing stores, creating backup..."))

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = os.Rename(utils.CurrentDir+utils.PathSeparator+utils.Patch+utils.DatastoreDir, utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir)

	bytes, err = os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + "temp-store" + utils.PathSeparator + "temp-file")

	assertions.Equal("temp store contents.", string(bytes))

}

func TestRollbackWithSufficientDiskSpace(t *testing.T) {

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	assertions := assert.New(t)

	utils.InitConfigs(utils.UpdateConfigs(configBytes, utils.MotadataMap{

		"datastore.minimum.available.disk.space.threshold.percent": 0.02,
	}))

	utils.Unzip(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))+utils.PathSeparator+"test-patch-files"+utils.PathSeparator+"patchv1_11.zip", utils.CurrentDir)

	dirs, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions.Nil(err)

	expectedDirs := len(dirs)

	version := "1.1.x"

	storeName := ""

	var bytes []byte

	for index := range dirs {

		if !dirs[index].IsDir() {

			continue
		}

		storeName = dirs[index].Name()

		bytes, err = os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + dirs[index].Name() + utils.PathSeparator + utils.MetadataFile)

		assertions.Nil(err)

		assertions.NotNil(bytes)

		metadata := utils.MotadataMap{}

		err = json.Unmarshal(bytes, &metadata)

		assertions.Nil(err)

		version = metadata.GetStringValue(utils.Version)

		break
	}

	_ = os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.DatastoreVariant, []byte(version), 0666)

	_ = os.Remove(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.HorizontalAggregations)

	assertions.NotNil(patchManager.ApplyPatches(version))

	dirs, err = os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions.Equal(expectedDirs, len(dirs))

	utils.AssertLogMessage(assertions, "Patch Manager", "patch", "failed to apply patch")

	utils.AssertLogMessage(assertions, "Patch Manager", "patch", fmt.Sprintf("restoring previous stable variant: %v", version))

	bytes, err = os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.DatastoreVariant)

	assertions.Nil(err)

	assertions.Equal(version, string(bytes))

	bytes, err = os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + storeName + utils.PathSeparator + utils.MetadataFile)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	metadata := utils.MotadataMap{}

	err = json.Unmarshal(bytes, &metadata)

	assertions.Nil(err)

	assertions.Equal(version, metadata.GetStringValue(utils.Version))

}

func TestRollbackWithInSufficientDiskSpace(t *testing.T) {

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	assertions := assert.New(t)

	utils.InitConfigs(utils.UpdateConfigs(configBytes, utils.MotadataMap{

		"datastore.minimum.available.disk.space.threshold.percent": 0.98,
	}))

	utils.Unzip(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))+utils.PathSeparator+"test-patch-files"+utils.PathSeparator+"patchv1_11.zip", utils.CurrentDir)

	dirs, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions.Nil(err)

	expectedDirs := len(dirs)

	version := "1.1.x"

	storeName := ""

	var bytes []byte

	for index := range dirs {

		if !dirs[index].IsDir() {

			continue
		}

		storeName = dirs[index].Name()

		bytes, err = os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + dirs[index].Name() + utils.PathSeparator + utils.MetadataFile)

		assertions.Nil(err)

		assertions.NotNil(bytes)

		metadata := utils.MotadataMap{}

		err = json.Unmarshal(bytes, &metadata)

		assertions.Nil(err)

		version = metadata.GetStringValue(utils.Version)

		break
	}

	_ = os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.DatastoreVariant, []byte(version), 0666)

	_ = os.Remove(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.HorizontalAggregations)

	assertions.NotNil(patchManager.ApplyPatches(version))

	dirs, err = os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions.Equal(expectedDirs, len(dirs))

	utils.AssertLogMessage(assertions, "Patch Manager", "patch", "not enough disk space to backup the previous data stores.")

	bytes, err = utils.ReadLogFile("Patch Manager", "patch")

	assertions.False(strings.Contains(string(bytes), "restoring previous stable variant"))

	bytes, err = os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.DatastoreVariant)

	assertions.Nil(err)

	assertions.Equal(version, string(bytes))

	bytes, err = os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + storeName + utils.PathSeparator + utils.MetadataFile)

	assertions.Nil(err)

	assertions.NotNil(bytes)

	metadata := utils.MotadataMap{}

	err = json.Unmarshal(bytes, &metadata)

	assertions.Nil(err)

	assertions.Equal(version, metadata.GetStringValue(utils.Version))

}

func TestApplyPatchV1_1_6(t *testing.T) {

	utils.InitConfigs(utils.UpdateConfigs(configBytes, utils.MotadataMap{

		"datastore.minimum.available.disk.space.threshold.percent": 0.02,
		"datastore.horizontal.aggregation.timer.seconds":           1,
	}))

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	patchv1_11.ProbeTimerSeconds = 2

	datastore.Init()

	assertions := assert.New(t)

	datastoreVariantFile := utils.CurrentDir + utils.PathSeparator + utils.DatastoreVariant

	err := os.WriteFile(datastoreVariantFile, []byte("1.1.6"), 0666)

	assertions.Nil(err)

	utils.Unzip(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))+utils.PathSeparator+"test-patch-files"+utils.PathSeparator+"patchv1_11.zip", utils.CurrentDir)

	bytes, err := os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.ColumnConfigFile)

	assertions.Nil(err)

	columns := make(utils.MotadataMap)

	err = json.Unmarshal(bytes, &columns)

	assertions.Nil(err)

	columns.GetMapValue(utils.IndexableColumns)["500000-flow"] = utils.MotadataMap{}

	columns[utils.ColumnEncoders] = utils.MotadataMap{}

	assertions.Nil(patches.UpdateColumnConfigs(columns))

	patchManager.ApplyPatches("1.1.6")

	_, err = os.Stat(utils.CurrentDir + utils.PathSeparator + utils.Patch + utils.DatastoreDir)

	assertions.NotNil(err)

	assertions.True(os.IsNotExist(err))

	bytes, err = os.ReadFile(datastoreVariantFile)

	assertions.Equal(utils.StoreVariant, string(bytes))

	assertions.Nil(err)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	bytes, err = os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + "horizontal-aggregations.json")

	assert.Nil(t, err)

	assert.NotNil(t, bytes)

	horizontalAggregations := utils.MotadataMap{}

	err = json.Unmarshal(bytes, &horizontalAggregations)

	assert.Nil(t, err)

	policy := horizontalAggregations.GetMapValue("600006-policy")

	indexes := policy.GetMapValue("600006-policy@@@0").GetMapValue("indexable.columns")

	assert.True(t, indexes.Contains("event.source"))

	assert.True(t, indexes.Contains("policy.type"))

	assert.Equal(t, 15, policy.GetMapValue("600006-policy@@@0").GetIntValue("type"))
}

func TestApplyPatchV1_15(t *testing.T) {

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	datastore.Init()

	assertions := assert.New(t)

	datastoreVariantFile := utils.CurrentDir + utils.PathSeparator + utils.DatastoreVariant

	err := os.WriteFile(datastoreVariantFile, []byte("1.32"), 0666)

	assertions.Nil(err)

	utils.Unzip(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))+utils.PathSeparator+"test-patch-files"+utils.PathSeparator+"patchv1_11.zip", utils.CurrentDir)

	bytes, err := os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.ColumnConfigFile)

	assertions.Nil(err)

	columns := make(utils.MotadataMap)

	err = json.Unmarshal(bytes, &columns)

	assertions.Nil(err)

	columns.GetMapValue(utils.IndexableColumns)["500000-flow"] = utils.MotadataMap{}

	columns[utils.ColumnEncoders] = utils.MotadataMap{}

	assertions.Nil(patches.UpdateColumnConfigs(columns))

	err = patchManager.ApplyPatches("1.32")

	assertions.Nil(err)

	bytes, err = os.ReadFile(datastoreVariantFile)

	assertions.Equal(utils.StoreVariant, string(bytes))

	assertions.Nil(err)

	bytes, err = utils.ReadLogFile("Patch Manager", "patch")

	assertions.Nil(err)

	assertions.False(strings.Contains(string(bytes), "disk space available for taking backup of existing stores, creating backup..."))

	assertions.False(strings.Contains(string(bytes), "upgradable patches found."))

	assertions.False(strings.Contains(string(bytes), fmt.Sprintf("restoring previous stable variant: 1.15")))
}

func TestApplyPatchV1_55(t *testing.T) {

	utils.InitConfigs(utils.UpdateConfigs(configBytes, utils.MotadataMap{

		"datastore.minimum.available.disk.space.threshold.percent": 0.02,
	}))

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	assertions := assert.New(t)

	datastoreVariantFile := utils.CurrentDir + utils.PathSeparator + utils.DatastoreVariant

	err := os.WriteFile(datastoreVariantFile, []byte("1.54"), 0666)

	assertions.Nil(err)

	err = utils.Unzip(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))+utils.PathSeparator+"test-patch-files"+utils.PathSeparator+"patchv1_54.zip", utils.CurrentDir)

	assertions.NoError(err)

	patchManager = NewPatchManager()

	err = patchManager.ApplyPatches("1.54")

	assertions.NoError(err)

	variant, err := os.ReadFile(datastoreVariantFile)

	assertions.Equal("1.55", string(variant))

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

}

func TestApplyRedundantPatch(t *testing.T) {

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	datastore.Init()

	assertions := assert.New(t)

	patchManager.tokenizer = &utils.Tokenizer{Tokens: make([]string, utils.TokenizerLength)}

	err := patchManager.ApplyPatches("1.13")

	assertions.Nil(err)

	utils.AssertLogMessageInverse(assertions, "Patch Manager", "patch", "applying patch 1.13")
}

func TestPatchPanicRecovery(t *testing.T) {

	assertions := assert.New(t)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	utils.Unzip(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))+utils.PathSeparator+"test-patch-files"+utils.PathSeparator+"patchv1_11.zip", utils.CurrentDir)

	patchManager.tokenizer = nil

	patchManager.ApplyPatches("1.11")

	utils.AssertLogMessage(assertions, "Patch Manager", "patch", "occurred in patch manager while applying patches")

	patchManager.tokenizer = &utils.Tokenizer{Tokens: make([]string, utils.TokenizerLength)}

}

// keep this test case in last as it is removing the datastore directory
func TestUpdateStoreVariant(t *testing.T) {

	maxvalueBufferBytes := utils.MaxValueBufferBytes

	defer func() {

		utils.MaxValueBufferBytes = maxvalueBufferBytes
	}()

	utils.MaxValueBufferBytes = 0

	_ = NewPatchManager()

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	assertions := assert.New(t)

	err := os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	assertions.Nil(err)

	err = patches.UpdateStoreVariant("temp-store", "1.11")

	assertions.NotNil(err)

}
