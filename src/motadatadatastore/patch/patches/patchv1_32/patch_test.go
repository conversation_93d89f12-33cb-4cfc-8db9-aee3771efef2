/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05			 <PERSON><PERSON><PERSON>-5190  Migrated constants from datastore to utils to match SonarQube Standard
 */

package patchv1_32

import (
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"motadatadatastore/utils"
	"os"
	"testing"
)

func TestMain(m *testing.M) {

	m.Run()
}

func TestPatch(t *testing.T) {

	assertions := assert.New(t)

	_ = os.Remove(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	_ = os.Remove(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.TempPatch + utils.HyphenSeparator + utils.ColumnConfigFile)

	assertions.Error(Patch())

	_ = os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.MotadataDatastoreConfigFile, []byte("dummy"), 0755)

	assertions.Error(Patch())

	configs := utils.MotadataMap{

		DatastoreDataWriterValueBufferBytesLength: 100,
		DatastoreDataWriterTXNBufferBytesLength:   100,
		DatastoreConfigWriters:                    100,
		DatastoreHealthWriters:                    100,
		HealthJobFlushTimerSeconds:                100,
	}

	_ = os.MkdirAll(utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.TempPatch+utils.HyphenSeparator+utils.MotadataDatastoreConfigFile, 0755)

	bytes, _ := json.Marshal(configs)

	_ = os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.ConfigDir+utils.PathSeparator+utils.MotadataDatastoreConfigFile, bytes, 0755)

	assertions.Error(Patch())

	os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.TempPatch + utils.HyphenSeparator + utils.MotadataDatastoreConfigFile)

	assertions.NoError(Patch())

	bytes, _ = os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	configs = utils.MotadataMap{}

	json.Unmarshal(bytes, &configs)

	assertions.Contains(configs, "datastore.data.writer.value.buffer.bytes")

	assertions.Contains(configs, "datastore.data.writer.txn.buffer.bytes")

	assertions.Contains(configs, "datastore.static.metric.writers")

	assertions.Contains(configs, "datastore.health.metric.writers")

	assertions.Contains(configs, "health.metric.flush.timer.seconds")

	_ = os.Remove(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.TempPatch + utils.HyphenSeparator + utils.MotadataDatastoreConfigFile)

}
