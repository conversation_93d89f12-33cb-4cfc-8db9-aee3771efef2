/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-02-10			 <PERSON><PERSON>val <PERSON>			Motadata-4913  Added patch for variant 1.48
* 2025-02-28			 <PERSON><PERSON><PERSON>-5194  Added patch for variant 1.49
* 2025-04-09			 <PERSON><PERSON><PERSON>ata-4859  Refactoring "fmt.Sprintf" to update go version to 1.24
* 2025-05-05			 <PERSON>wa<PERSON><PERSON>l <PERSON><PERSON> Dave		M<PERSON>ADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-05-10             <PERSON><PERSON><PERSON>            MOTADATA-6174  Added Unmap function to release memory-mapped resources
* 2025-06-23             Vedant Dokania         Motadata-6370 Mapping operand changes to get the instance type store
* 2025-06-04             Aashil Shah            MOTADATA-5780 Called custom MmapAnonymous and Munmap functions and replaced Statfs logic
 */

package patch

import (
	"errors"
	"fmt"
	cp "github.com/otiai10/copy"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/patch/patches/patchv1_11"
	"motadatadatastore/patch/patches/patchv1_21"
	"motadatadatastore/patch/patches/patchv1_23"
	"motadatadatastore/patch/patches/patchv1_31"
	"motadatadatastore/patch/patches/patchv1_32"
	"motadatadatastore/patch/patches/patchv1_43"
	"motadatadatastore/patch/patches/patchv1_44"
	"motadatadatastore/patch/patches/patchv1_46"
	"motadatadatastore/patch/patches/patchv1_48"
	"motadatadatastore/patch/patches/patchv1_49"
	"motadatadatastore/patch/patches/patchv1_55"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"runtime"
	"strings"
	"sync"
)

var logger = utils.NewLogger("Patch Manager", "patch")

var upgradablePatches = []float64{1.11, 1.31}

type PatchManager struct {
	tokenizer *utils.Tokenizer

	variant float64

	keyBuffers, valueBuffers [][]byte

	events []storage.DiskIOEventBatch

	event storage.DiskIOEvent

	encoder codec.Encoder

	waitGroup *sync.WaitGroup
}

func NewPatchManager() *PatchManager {

	variant := codec.StringToFloat64(utils.StoreVariant)

	events := make([]storage.DiskIOEventBatch, utils.MaxStoreParts)

	for i := 0; i < len(events); i++ {

		events[i] = storage.DiskIOEventBatch{}
	}

	event := storage.DiskIOEvent{}

	valueBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	keyBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	for i := 0; i < len(keyBuffers); i++ {

		bytes, err := utils.MmapAnonymous(utils.MaxValueBufferBytes)

		if err != nil {

			bytes = make([]byte, utils.MaxValueBufferBytes)
		}

		valueBuffers[i] = bytes
	}

	return &PatchManager{
		tokenizer: &utils.Tokenizer{Tokens: make([]string, utils.TokenizerLength)},

		variant: variant,

		events: events,

		event: event,

		keyBuffers: keyBuffers,

		valueBuffers: valueBuffers,

		waitGroup: &sync.WaitGroup{},

		encoder: codec.NewEncoder(utils.NewMemoryPool(10, utils.MaxPoolLength, true, utils.DefaultBlobPools)),
	}
}

func (patchManager *PatchManager) ApplyPatches(currentVariant string) (err error) {

	defer func() {

		if r := recover(); r != nil {

			stackTraceBytes := make([]byte, 1<<20)

			logger.Error(fmt.Sprintf("error %v occurred in patch manager while applying patches", r))

			logger.Error(fmt.Sprintf("!!!STACK TRACE for patch manager !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			err = errors.New(fmt.Sprintf("error %v occurred in patch manager while applying patches", r))
		}

		if err != nil {

			patchManager.rollbackPatch(currentVariant)
		}

	}()

	if _, err = os.Stat(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir); os.IsNotExist(err) {

		logger.Info("no datastores found to apply patch.")

		if _, err = os.Stat(utils.CurrentDir + utils.PathSeparator + utils.Patch + utils.DatastoreDir); os.IsNotExist(err) {

			return patchManager.save(utils.StoreVariant)
		}

		logger.Info("found previous backup...restoring the previous backup of datastores to apply patch.")

		//in-case the datastore process previously shutdown while rollback the previous datastores

		if err = os.Rename(utils.CurrentDir+utils.PathSeparator+utils.Patch+utils.DatastoreDir, utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir); err != nil {

			logger.Error(fmt.Sprintf("failed to rename backup directory of data stores, reason: %v", err.Error()))

			return err
		}
	}

	logger.Info(fmt.Sprintf("current variant: %v, required variant: %v", currentVariant, utils.StoreVariant))

	utils.Split(currentVariant, utils.DotSeparator, patchManager.tokenizer)

	variant := float64(0)

	if patchManager.tokenizer.Counts == 3 {

		variant = codec.StringToFloat64(strings.Join(patchManager.tokenizer.Tokens[:patchManager.tokenizer.Counts-1], utils.DotSeparator))

	} else {

		variant = codec.StringToFloat64(currentVariant)
	}

	if variant < upgradablePatches[len(upgradablePatches)-1] {

		logger.Info("upgradable patches found.")

		if !patchManager.backup() {

			logger.Error(fmt.Sprintf("failed to upgrade to %v, reason: unable to backup datastores", utils.StoreVariant))

			return errors.New(fmt.Sprintf("failed to upgrade to %v, reason: unable to backup datastores", utils.StoreVariant))
		}

	}

	datastore.Init()

	defer datastore.Close()

	for variant < patchManager.variant {

		variant = utils.ToFixed(variant + 0.01)

		logger.Info(fmt.Sprintf("applying patch %v", variant))

		if variant == 1.11 {

			err = patchv1_11.Patch(patchManager.valueBuffers, patchManager.encoder, patchManager.tokenizer, patchManager.events, patchManager.waitGroup)

		} else if variant == 1.21 {

			err = patchv1_21.Patch(patchManager.tokenizer)

		} else if variant == 1.23 {

			err = patchv1_23.Patch()

		} else if variant == 1.31 {

			err = patchv1_31.Patch()

		} else if variant == 1.32 {

			err = patchv1_32.Patch()

		} else if variant == 1.43 {

			err = patchv1_43.Patch()

		} else if variant == 1.44 {

			err = patchv1_44.Patch()

		} else if variant == 1.46 {

			err = patchv1_46.Patch()

		} else if variant == 1.48 {

			err = patchv1_48.Patch()

		} else if variant == 1.49 {

			err = patchv1_49.Patch()

		} else if variant == 1.55 {

			err = patchv1_55.Patch(patchManager.tokenizer, patchManager.encoder)

		}

		if err != nil {

			logger.Error(fmt.Sprintf("failed to apply patch %v, reason: %v", variant, err.Error()))

			return errors.New(fmt.Sprintf("aborting patch as patch manager failed to apply patch %v, reason: %v", variant, err.Error()))
		}

		if err = patchManager.save(utils.FLOAT64ToStringValue(variant)); err != nil {

			return err
		}

		logger.Info(fmt.Sprintf("patch %v successfully applied", variant))

	}

	patchManager.cleanup() //do not put this in defer

	return nil

}

func (patchManager *PatchManager) save(variant string) error {

	if err := os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.DatastoreVariant, []byte(variant), 0666); err != nil {

		logger.Error(fmt.Sprintf("failed to update datastore-variant %v, reason: %v", variant, err.Error()))

		return errors.New(fmt.Sprintf("failed to update datastore-variant %v, reason: %v", variant, err.Error()))
	}

	return nil

}

func (patchManager *PatchManager) cleanup() {

	for index := range patchManager.valueBuffers {

		if err := utils.Munmap(patchManager.valueBuffers[index]); err != nil {

			logger.Warn(fmt.Sprintf("failed to unmap anonymous mapped buffer,reason: %v", err.Error()))
		}
	}

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.Patch + utils.DatastoreDir)

	patchManager.encoder.MemoryPool.Unmap()

}

func (patchManager *PatchManager) rollbackPatch(variant string) {

	if _, err := os.Stat(utils.CurrentDir + utils.PathSeparator + utils.Patch + utils.DatastoreDir); err == nil {

		logger.Info(fmt.Sprintf("restoring previous stable variant: %v", variant))

		if err = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir); err != nil {

			logger.Error(fmt.Sprintf("failed to rollback patch, reason: unable to remove data stores, reason: %v", err.Error()))
		}

		if err = os.Rename(utils.CurrentDir+utils.PathSeparator+utils.Patch+utils.DatastoreDir, utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir); err != nil {

			logger.Error(fmt.Sprintf("failed to rollback patch, reason: unable to rename backup directory of data stores, reason: %v", err.Error()))
		}
	}

	_ = os.WriteFile(utils.CurrentDir+utils.PathSeparator+utils.DatastoreVariant, []byte(variant), 0666)

}

func (patchManager *PatchManager) backup() bool {

	availableDiskSizeBytes, totalDiskSizeBytes, _ := utils.GetStats()

	logger.Info(fmt.Sprintf("available disk size bytes: %v", availableDiskSizeBytes))

	logger.Info(fmt.Sprintf("total disk size bytes: %v", totalDiskSizeBytes))

	logger.Info(fmt.Sprintf("available disk size percent: %v", float64(availableDiskSizeBytes)/float64(totalDiskSizeBytes)))

	if float64(availableDiskSizeBytes)/float64(totalDiskSizeBytes) >= utils.GetMinimumDiskSpaceAvailableThresholdPercent() {

		logger.Info("disk space available for taking backup of existing stores, creating backup...")

		if _, err := os.Stat(utils.CurrentDir + utils.PathSeparator + utils.Patch + utils.DatastoreDir); err == nil {

			_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.Patch + utils.DatastoreDir)
		}

		if err := os.MkdirAll(utils.CurrentDir+utils.PathSeparator+utils.Patch+utils.DatastoreDir, 0755); err != nil {

			logger.Error(fmt.Sprintf("failed to create backup directory for datastore, reason: %v", err.Error()))

			return false
		}

		if err := cp.Copy(utils.CurrentDir+utils.PathSeparator+utils.DatastoreDir, utils.CurrentDir+utils.PathSeparator+utils.Patch+utils.DatastoreDir); err != nil {

			logger.Error(fmt.Sprintf("failed to copy datastore to backup directory, reason: %v", err.Error()))

			return false
		}

		return true
	}

	logger.Error("not enough disk space to backup the previous data stores.")

	return false
}
