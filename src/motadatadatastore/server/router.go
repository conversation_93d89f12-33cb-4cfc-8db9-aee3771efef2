/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05             Vedant Dokania         Motadata-5451  Routing status flap metric request
* 2025-05-10             Dhaval <PERSON>ra            MOTADATA-6174  Added Unmap function to release memory-mapped resources
 */

/*
	there are two types of router, notification and reader.
	In notification router we get request for widget create , retention job , and backup job from MOTADATA,
	while in reader we get request for query and query abort ( query abort is for long-running queries)
*/

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-21			 Dhaval <PERSON>ra			Motadata-5452  Resolved Query Engine Type for NetRouteMetric DatastoreType
* 2025-04-02			 Dhaval Bera			Motadata-4859  Added NetRoute Status Metric Datastore Type
* 2025-05-05			 Swapnil A. Dave		MOTADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-05-26  			 Dhaval Bera			MOTADATA-6333 Discarded Query which exceeds query start time threshold
* 2025-06-03			 Dhaval Bera			Motadata-6393  Updated With Master Branch
* 2025-06-24			 Dhaval Bera			Motadata-6639  Added Retention Check In Query Parsing

 */

// Package server implements the central coordination layer of the Motadata Database system.
// It manages the lifecycle of the database server, initializes and coordinates all system components,
// handles communication channels, manages configuration changes, implements graceful shutdown,
// and monitors system health and performance.
//
// The server package contains several key components:
// - RequestRouter: Handles routing of client requests to appropriate executors
// - Server: Manages the overall server lifecycle and component coordination
// - Configuration: Handles server configuration and dynamic updates
// - Health monitoring: Tracks system health metrics and performance
package server

import (
	bytes2 "bytes"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/emirpasic/gods/maps/linkedhashmap"
	"github.com/golang/snappy"
	"github.com/tidwall/gjson"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/datastore/query"
	"motadatadatastore/utils"
	"os"
	"runtime"
	"strings"
	"sync/atomic"
	"time"
)

var (
	routerLogger = utils.NewLogger("Request Router", "server")

	queryEngineTypes = []query.QueryEngineType{query.Metric, query.Flow, query.Log, query.DrillDown, query.AIOps}

	MaxPriority = 5

	queueSize = 50

	queryLatencyMetricsByQueryEngineType = map[query.QueryEngineType]string{
		query.Log:       utils.LogQueryLatency,
		query.Metric:    utils.MetricQueryLatency,
		query.AIOps:     utils.AIOpsQueryLatency,
		query.DrillDown: utils.DrillDownQueryLatency,
		query.Flow:      utils.FlowQueryLatency,
	}

	Acknowledged atomic.Bool
)

const (
	RouterTypeNotification RequestRouterType = iota

	RouterTypeReader
)

const OperationType = 0

const (
	errorQueryRouter = "error %v occurred in query router"

	errorStackTraceQueryRouter = "!!!STACK TRACE for query router!!! \n %v"
)

// RequestRouterType defines the type of request router.
// There are two types: notification router and reader router.
type RequestRouterType int

// RequestRouter handles the routing of client requests to appropriate query executors.
// It manages request queues, prioritizes queries, tracks running queries, and handles
// query responses. The router supports two modes of operation:
// 1. Notification router: Handles requests for widget creation, retention jobs, and backup jobs
// 2. Reader router: Handles query requests and query abort requests
//
// The router implements sophisticated query queuing and prioritization mechanisms to ensure
// efficient utilization of query executors while maintaining fairness and responsiveness.
type RequestRouter struct {
	// memoryPool manages memory allocation for request processing to reduce GC pressure
	memoryPool *utils.MemoryPool

	// routerType determines the mode of operation (notification or reader)
	routerType RequestRouterType

	// executorAllocations maps executor IDs to their assigned query engine types
	executorAllocations map[int]query.QueryEngineType

	// queuedQueries stores pending queries organized by query engine type and priority
	queuedQueries map[query.QueryEngineType][]*linkedhashmap.Map

	// priorities tracks the current priority level for each query engine type
	priorities map[query.QueryEngineType]int

	// drillDownRequests is a channel for drill-down query requests
	// Requests is a channel for all incoming requests
	drillDownRequests, Requests chan []byte

	// Responses is a channel for query responses to be sent back to clients
	Responses chan string

	// ShutdownNotifications is a channel for shutdown signals
	ShutdownNotifications chan bool

	// queryTrackers maps query IDs to their start timestamps for tracking execution time
	queryTrackers map[int64]int64

	// drillDownQueries maps parent query IDs to the count of their drill-down queries
	drillDownQueries map[int64]int

	// drillDownQueryContexts maps drill-down query IDs to their context data
	drillDownQueryContexts map[int64][]byte

	// runningQueries maps parent query IDs to a set of their sub-query IDs
	// Used to track and manage query hierarchies
	runningQueries map[int64]map[int64]struct{}

	// executors tracks the busy status of query executors
	executors map[int]bool

	// idleExecutors is a list of available executor IDs
	idleExecutors []int

	// abortTimerThresholdMillis is the threshold in milliseconds after which long-running queries are aborted
	abortTimerThresholdMillis int64

	// abortedQueries counts the number of queries that have been aborted
	// aggregationQueries counts the number of aggregation queries processed
	abortedQueries, aggregationQueries int

	// queriesByQueryEngineType counts the number of queries processed by each query engine type
	queriesByQueryEngineType map[query.QueryEngineType]int

	// queryLatencies tracks the cumulative query latency for each query engine type
	queryLatencies map[query.QueryEngineType]int64

	// queryEngineTypesByQueryId maps query IDs to their query engine types
	queryEngineTypesByQueryId map[int64]query.QueryEngineType

	discardedQueries int
}

// NewRequestRouter creates and initializes a new RequestRouter instance.
//
// Parameters:
//   - routerType: Determines the mode of operation (notification or reader)
//   - executorAllocations: Maps executor IDs to their assigned query engine types
//
// Returns:
//   - A fully initialized RequestRouter instance ready to handle requests
//
// The function initializes different data structures based on the router type:
// - For notification routers: Basic channels for request handling
// - For reader routers: Additional structures for query queuing, tracking, and executor management
func NewRequestRouter(routerType RequestRouterType, executorAllocations map[int]query.QueryEngineType) *RequestRouter {

	pool := utils.NewMemoryPool(4, utils.MaxPoolLength, false, utils.DefaultBlobPools)

	_, err := os.Stat(utils.JobDir)

	if os.IsNotExist(err) {

		_ = os.Mkdir(utils.JobDir, 0755)
	}

	var queuedQueries map[query.QueryEngineType][]*linkedhashmap.Map

	var priorities map[query.QueryEngineType]int

	var queryExecutors map[int]bool

	var runningQueries map[int64]map[int64]struct{}

	var drillDownQueryContexts map[int64][]byte

	var drillDownQueries map[int64]int

	var queryTrackers map[int64]int64

	var idleExecutors []int

	if routerType == RouterTypeReader {

		queryExecutors = make(map[int]bool, utils.QueryExecutors)

		runningQueries = make(map[int64]map[int64]struct{}, queueSize*5)

		drillDownQueryContexts = make(map[int64][]byte, 1000)

		drillDownQueries = make(map[int64]int, 1000)

		queryTrackers = make(map[int64]int64, 1000)

		idleExecutors = make([]int, utils.QueryExecutors)

		queuedQueries = make(map[query.QueryEngineType][]*linkedhashmap.Map, len(queryEngineTypes))

		priorities = make(map[query.QueryEngineType]int, len(queryEngineTypes))

		for i := 0; i < len(queryEngineTypes); i++ {

			queries := make([]*linkedhashmap.Map, MaxPriority)

			for j := range queries {

				queries[j] = linkedhashmap.New()
			}

			queuedQueries[queryEngineTypes[i]] = queries

			priorities[queryEngineTypes[i]] = 0
		}
	}

	return &RequestRouter{

		executorAllocations: executorAllocations,

		Requests: make(chan []byte, utils.GetDatastoreEventChannelSize()),

		ShutdownNotifications: make(chan bool, 5),

		memoryPool: pool,

		executors: queryExecutors,

		Responses: make(chan string, utils.QueryExecutors),

		runningQueries: runningQueries,

		routerType: routerType,

		drillDownRequests: make(chan []byte, utils.DrillDownQueryExecutors),

		drillDownQueryContexts: drillDownQueryContexts,

		drillDownQueries: drillDownQueries,

		queryTrackers: queryTrackers,

		queuedQueries: queuedQueries,

		priorities: priorities,

		idleExecutors: idleExecutors,

		abortTimerThresholdMillis: int64(utils.GetQueryAbortTimerSeconds() * 1000),

		queryEngineTypesByQueryId: make(map[int64]query.QueryEngineType),

		queryLatencies: make(map[query.QueryEngineType]int64),

		queriesByQueryEngineType: make(map[query.QueryEngineType]int),
	}

}

// Start launches the request router in a separate goroutine.
//
// This method starts the router in either notification mode or reader mode based on the router's type.
// It runs asynchronously and returns immediately while the router continues to process requests
// in the background until a shutdown signal is received.
//
// For notification routers: Handles widget creation, retention jobs, and backup jobs
// For reader routers: Handles query requests and query abort requests
func (router *RequestRouter) Start() {
	go func() {
		if router.routerType == RouterTypeNotification {
			router.startNotification()
		} else {
			router.startReader()
		}
	}()
}

// startNotification begins the notification router's main processing loop.
//
// This method handles requests for widget creation, retention jobs, and backup jobs.
// It continuously listens for incoming requests on the Requests channel and routes them
// to the appropriate handlers. The method also listens for shutdown signals and performs
// cleanup when a shutdown is requested.
//
// The method includes panic recovery to ensure that any unexpected errors are logged
// and the system can attempt to gracefully shut down rather than crashing completely.
func (router *RequestRouter) startNotification() {
	defer func() {
		if err := recover(); err != nil {
			stackTraceBytes := make([]byte, 1<<20)
			routerLogger.Error(fmt.Sprintf("error %v occurred in notification router", err))
			routerLogger.Error(fmt.Sprintf("!!!STACK TRACE for notification router!!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
			utils.ShutdownNotifications <- "Router" + utils.GroupSeparator + "Shutting down notification router" + utils.GroupSeparator + fmt.Sprintf("%v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))
			return
		}
	}()

	for {

		select {

		case bytes := <-router.Requests:

			router.routeNotificationRequests(bytes)

		case <-router.ShutdownNotifications:

			router.memoryPool.Unmap()

			routerLogger.Info("shutting down notification request router...")

			return

		}
	}

}

// startReader begins the reader router's main processing loop.
//
// This method handles query requests and query abort requests. It manages query queues,
// dispatches queries to available executors, tracks query execution, and handles query
// responses. The method implements sophisticated query prioritization and load balancing
// to ensure efficient utilization of query executors.
//
// The method continuously listens for:
// - Incoming query requests on the Requests channel
// - Drill-down query requests on the drillDownRequests channel
// - Query responses from executors on the Responses channel
// - Shutdown signals on the ShutdownNotifications channel
//
// It also includes panic recovery to ensure that any unexpected errors are logged
// and the system can attempt to gracefully shut down rather than crashing completely.
func (router *RequestRouter) startReader() {
	defer func() {
		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			routerLogger.Error(fmt.Sprintf(errorQueryRouter, err))

			routerLogger.Error(fmt.Sprintf(errorStackTraceQueryRouter, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			utils.ShutdownNotifications <- "Router" + utils.GroupSeparator + "Shutting down query router" + utils.GroupSeparator + fmt.Sprintf("%v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))

			return
		}
	}()

	tokenizer := &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	queueSize = utils.GetQueryQueueSize()

	// Initialize memory tracking for queries
	utils.InitializeMemoryManager()

	for index := 0; index < utils.QueryExecutors; index++ {

		router.executors[index] = true
	}

	queryAbortTimer := time.NewTicker(time.Second * time.Duration(30))

	for {

		select {

		case bytes := <-router.drillDownRequests:

			router.drillDownQueryContexts[codec.ReadINT64Value(bytes[:8])] = bytes[8:]

		case <-queryAbortTimer.C:

			router.abort()

		case bytes := <-router.Requests:

			router.routeReadRequest(bytes)

		case response := <-router.Responses:

			router.processQueuedQuery(response, tokenizer)

		case <-utils.HealthCheckupJobRequests:

			router.publishHealthMetrics()

		case <-router.ShutdownNotifications:

			router.memoryPool.Unmap()

			routerLogger.Info("shutting down query request router...")

			return
		}
	}
}

// routeReadRequest processes and routes incoming query requests.
//
// Parameters:
//   - bytes: The raw request data received from the client
//
// This method is responsible for:
// 1. Parsing and validating the query request
// 2. Determining the query type and priority
// 3. Handling query abort requests separately
// 4. Enqueuing regular queries based on their type and priority
// 5. Dispatching queries to available executors when possible
//
// The method implements sophisticated query routing logic to ensure that
// queries are processed efficiently while maintaining fairness across
// different query types and priorities.
func (router *RequestRouter) routeReadRequest(bytes []byte) {
	// Set up panic recovery to ensure the router continues running even if an error occurs
	// This is critical for system stability as the router is a central component
	defer func() {
		if err := recover(); err != nil {
			// Allocate a large buffer for the stack trace (1MB)
			stackTraceBytes := make([]byte, 1<<20)
			// Log the error and stack trace for debugging
			routerLogger.Error(fmt.Sprintf(errorQueryRouter, err))

			routerLogger.Error(fmt.Sprintf(errorStackTraceQueryRouter, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}
	}()

	// Only process if we have data
	if len(bytes) > 0 {
		// Create a map to hold the query context
		// This will store all parameters and metadata for the query
		context := utils.MotadataMap{}

		// Parse the JSON payload, skipping the first byte which contains the operation type
		// Store each key-value pair in the context map
		gjson.ParseBytes(bytes[1:]).ForEach(func(key, value gjson.Result) bool {
			context[key.String()] = value.Value()
			return true
		})

		// Route the request based on the operation type (first byte of the message)
		switch int(bytes[OperationType]) {

		case utils.QueryAbort:
			// Handle query abort request
			// This is triggered when a client wants to cancel a running or queued query
			queryId := context.GetInt64Value(query.QueryId)

			// Increment the counter for aborted queries (used for metrics)
			router.abortedQueries += 1

			routerLogger.Info(fmt.Sprintf("query abort received for query id %v", queryId))

			// Clean up drill-down related data for this query
			// Drill-down queries are hierarchical, so we need to clean up any related data
			delete(router.drillDownQueries, queryId)

			delete(router.drillDownQueryContexts, queryId)

			// If we were tracking this query's execution time, finalize the metrics
			// This ensures we still capture performance data even for aborted queries
			if _, ok := router.queryTrackers[queryId]; ok {
				// Calculate the total query latency and add it to the metrics
				router.queryLatencies[router.queryEngineTypesByQueryId[queryId]] += time.Now().UnixMilli() - router.queryTrackers[queryId]

				// Clean up the tracking data
				delete(router.queryTrackers, queryId)

				delete(router.queryEngineTypesByQueryId, queryId)
			}

			// If this query has sub-queries running, abort them all
			// Complex queries are often split into multiple sub-queries that run in parallel
			if _, ok := router.runningQueries[queryId]; ok {
				// Iterate through all sub-queries for this query ID
				for subQueryId := range router.runningQueries[queryId] {

					routerLogger.Info(fmt.Sprintf("sub query %v abort notification send", subQueryId))

					// Remove the sub-query from the running queries map
					delete(router.runningQueries[queryId], subQueryId)

					// Remove the sub-query from any priority queues it might be in
					// This prevents it from being executed if it hasn't started yet
					for _, queue := range router.queuedQueries {

						for index := range queue {

							if _, found := queue[index].Get(subQueryId); found {

								queue[index].Remove(subQueryId)

								break

							}
						}
					}

					// If the abort request includes a status, dispatch an error message
					// This notifies the client that the query was aborted
					if context.Contains(datastore.Status) {

						context[query.SubQueryId] = subQueryId

						router.dispatchErrors(errors.New(utils.ErrorQueryAborted), context, utils.Aborted)
					}

					// Convert the sub-query ID to string format for the abort channel
					subQueryId := codec.INT64ToStringValue(subQueryId)

					// Send abort signals to all executors
					// This ensures that even if the sub-query is currently executing, it will be stopped
					for executor := range executors {

						executors[executor].AbortRequests <- subQueryId

					}
				}

				// If all sub-queries have been removed, clean up the parent query entry
				if len(router.runningQueries[queryId]) == 0 {

					delete(router.runningQueries, queryId)

				}
			}

			// Log the number of pending queries if debug is enabled
			if utils.DebugEnabled() {

				routerLogger.Debug(fmt.Sprintf("pending queries %v", len(router.runningQueries)))

			}

		case utils.DatastoreRead:
			// Handle datastore read request (query execution)
			// This is the main path for processing client queries

			// Log query reception if debug is enabled
			if utils.DebugEnabled() {
				// Special handling for aggregation job queries to track their source
				if context.Contains(utils.AggregationJobId) {

					routerLogger.Debug(fmt.Sprintf("query request received from %v...", context.GetIntValue(utils.AggregationJobId)))

				} else {

					routerLogger.Debug("query request received...")

				}
			}

			// Extract the query identifiers from the context
			// queryId is the parent query ID, while subQueryId identifies this specific sub-query
			queryId := context.GetInt64Value(query.QueryId)

			subQueryId := context.GetInt64Value(query.SubQueryId)

			// Default priority is P0 (normal priority)
			// This can be overridden in the query context
			priority := int(utils.P0)

			if context.Contains(utils.QueryPriority) {

				priority = context.GetIntValue(utils.QueryPriority)

			}

			queryCreationTime := int64(0)

			if context.Contains(query.QueryCreationTime) {

				queryCreationTime = context.GetInt64Value(query.QueryCreationTime)

				// allow aggregation query and report query
				if !(priority == int(utils.P1) || priority == int(utils.P4)) && (time.Now().Unix()-queryCreationTime) > int64(utils.QueryCreationAgeThresholdSeconds) {

					routerLogger.Warn(fmt.Sprintf("discarded the query, query id : %d , subquery id : %d", queryId, subQueryId))

					router.dispatchErrors(errors.New(utils.ErrorQueryCreationAgeThresholdLimitReached), context, utils.Failed)

					router.discardedQueries++

					return

				}

			}

			//to notify if query creation time is missed in any query
			if queryCreationTime == 0 {

				routerLogger.Warn(fmt.Sprintf("missing field query creation time for queryId %v", queryId))
			}

			// Determine the query engine type based on the query context
			// Different query types (metric, log, flow, etc.) are handled by different engines
			queryEngineType := getQueryEngineType(context)

			// Get the priority queues for this query engine type
			// Each engine type has its own set of priority queues
			queuedQueries := router.queuedQueries[queryEngineType]

			// P1 priority is used for aggregation queries
			// Track these separately for metrics and capacity planning
			if priority == int(utils.P1) {

				router.aggregationQueries += 1

			}

			// Increment the counter for this query engine type
			// Used for metrics and load balancing
			router.queriesByQueryEngineType[queryEngineType] += 1

			// Set the queue size limit based on configuration
			// This prevents queue overflow and resource exhaustion
			limit := queueSize

			// Get the number of idle executors that can handle this query type
			// This determines if we can execute the query immediately
			idleExecutors := router.getIdleExecutors(queryEngineType)

			// Count the total number of pending queries across all priority levels
			// Used for load balancing and queue management
			pendingQueries := getSize(queuedQueries)

			// Special handling for drill-down queries
			// These have different queue limits and ID management
			if queryEngineType == query.DrillDown {
				// For drill-down queries, use the parent query ID as the sub-query ID
				// This ensures proper tracking in the hierarchy
				subQueryId = queryId

				// Drill-down queries have a different queue limit based on executor count
				// This is because they're typically more resource-intensive
				limit = utils.DrillDownQueryExecutors * utils.MaxDrillDownEvents

				if utils.DebugEnabled() {

					routerLogger.Debug(fmt.Sprintf("pending drilldown query contexts %v", len(router.drillDownQueryContexts)))

				}

				// Check if this is a new drill-down query (not already tracked)
				if _, ok := router.drillDownQueries[queryId]; !ok {
					// If we've reached the limit for drill-down queries, reject this one
					// This prevents resource exhaustion from too many drill-down queries
					if len(router.drillDownQueries) == limit {

						if utils.DebugEnabled() {

							routerLogger.Debug(fmt.Sprintf("aborting %v query for %v , reason : query max limit reached", queryEngineType, subQueryId))

						}

						// Send an error response to the client
						router.dispatchErrors(errors.New(utils.ErrorQueryQueueMaxLimitReached), context, utils.Failed)

						return
					}
				}
			}

			// Initialize the running queries map for this query ID if it doesn't exist
			// This map tracks all sub-queries for a given parent query
			if _, ok := router.runningQueries[queryId]; !ok {

				router.runningQueries[queryId] = map[int64]struct{}{}

			}

			// Add this sub-query to the running queries map
			// Using a struct{} as the value saves memory (zero size)
			router.runningQueries[queryId][subQueryId] = struct{}{}

			// If there are idle executors and no pending queries, execute immediately
			// This fast path avoids queuing when resources are available
			if idleExecutors > 0 && pendingQueries == 0 {
				// Send the query directly to the first idle executor
				router.sendQuery(context, router.idleExecutors[0], priority, queryEngineType)

			} else {
				// We need to queue the query because either:
				// 1. No idle executors are available, or
				// 2. There are already pending queries (maintain FIFO order)

				// Check if the queue for this priority level is full
				// Aggregation queries (P1) are never rejected due to queue limits
				if queuedQueries[priority].Size() == limit && priority != int(utils.P1) {

					if utils.DebugEnabled() {

						routerLogger.Debug(fmt.Sprintf("aborting %v query for %v , reason : query max limit reached", queryEngineType, subQueryId))

					}

					// Send an error response to the client
					router.dispatchErrors(errors.New(utils.ErrorQueryQueueMaxLimitReached), context, utils.Failed)

				} else {
					// Add the query to the appropriate priority queue
					// The linked hash map maintains insertion order for FIFO processing
					queuedQueries[priority].Put(subQueryId, context)

					// If there are idle executors, try to process some queued queries
					// This can happen if queries were added to higher priority queues
					if idleExecutors > 0 {

						router.processQueuedQueries(queryEngineType, pendingQueries, idleExecutors)

					}
				}
			}

		case utils.HeartBeat:

			utils.KeepAliveNotifications <- struct{}{}
		}
	}

}

// routeNotificationRequests processes and routes incoming notification requests.
//
// Parameters:
//   - bytes: The raw request data received from the client
//
// This method is responsible for handling various types of notification requests, including:
// - Widget creation requests
// - Retention job requests
// - Backup job requests
// - Configuration update notifications
// - Health check requests
//
// The method parses the request, determines its type, and routes it to the appropriate
// handler based on the operation type specified in the request. It also handles
// error conditions and sends appropriate responses back to the client.
func (router *RequestRouter) routeNotificationRequests(bytes []byte) {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			routerLogger.Error(fmt.Sprintf("error %v occurred in notification router", err))

			routerLogger.Error(fmt.Sprintf("!!!STACK TRACE for notification router!!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		}

	}()

	switch int(bytes[OperationType]) {

	case utils.WidgetCreate:

		if utils.Aggregation {

			if utils.TraceEnabled() {

				routerLogger.Trace("widget create request arrived from motadata")
			}

			context := utils.MotadataMap{}

			gjson.ParseBytes(bytes[1:]).ForEach(func(key, value gjson.Result) bool {
				context[key.String()] = value.Value()
				return true
			})

			if utils.DebugEnabled() {

				routerLogger.Debug(fmt.Sprintf("context %v received for aggregation..", string(bytes[2:])))
			}

			context[utils.OperationType] = utils.WidgetCreate

			utils.ManagerNotifications <- context

		}

	case utils.WidgetDelete:

		if utils.Aggregation {

			context := utils.MotadataMap{}

			gjson.ParseBytes(bytes[1:]).ForEach(func(key, value gjson.Result) bool {
				context[key.String()] = value.Value()
				return true
			})

			if utils.DebugEnabled() {

				routerLogger.Debug(fmt.Sprintf("context %v received to delete aggregation view", string(bytes[2:])))
			}

			context[utils.OperationType] = utils.WidgetDelete

			utils.ManagerNotifications <- context

		}

	case utils.RetentionJob:

		context := map[int]map[string]int{}

		_ = json.Unmarshal(bytes[1:], &context)

		if utils.TraceEnabled() {

			routerLogger.Trace(fmt.Sprintf("request %v received for retention", context))
		}

		for _, executor := range executors {

			executor.RetentionChangeNotifications <- context
		}

		utils.StoreRetentionJobs <- context

	case utils.BackupJob:

		storeBackupJob.SnapshotNotifications <- true

	case utils.BackupProfile:

		/* will get the backup profile notification here

		sample notification :-

		{

		datastore.types ->["metric","log","trap"....]

		}

		*/

		context := utils.MotadataMap{}

		_ = json.Unmarshal(bytes[1:], &context)

		if utils.TraceEnabled() {

			routerLogger.Trace(fmt.Sprintf("request %v received for backup profile", context))
		}

		storeBackupJob.BackupProfileNotifications <- context

	case utils.HeartBeat:

		utils.KeepAliveNotifications <- struct{}{}

	case utils.ModifyLogLevel:

		//notification comes from UI side for changing log level

		context := utils.MotadataMap{}

		gjson.ParseBytes(bytes[1:]).ForEach(func(key, value gjson.Result) bool {
			context[key.String()] = value.Value()
			return true
		})

		utils.SetLogLevel(context.GetIntValue(utils.SystemLogLevel))

		serverLogger.Info(fmt.Sprintf("modify log level notification received from motadata, changing log level to %v", utils.GetLogLevel()))

		if utils.DebugEnabled() || utils.TraceEnabled() {

			go func() {

				_ = utils.ResetLogLevel(context.GetIntValue(LogLevelResetTimerSeconds))
			}()
		}

	case utils.Diagnostic:

		// Diagnostics Notification comes from Motadata for profiling

		go utils.StartDiagnostic()

	case utils.Acknowledge:

		routerLogger.Info("acknowledgement received for indexable columns from motadata")

		Acknowledged.Store(true)
	}
}

func (router *RequestRouter) processQueuedQueries(queryType query.QueryEngineType, pendingQueries, idleExecutors int) {

	defer func() {

		if err := recover(); err != nil {

			routerLogger.Error(fmt.Sprintf("error %v occurred", err))
		}
	}()

	if idleExecutors == utils.NotAvailable {

		idleExecutors = router.getIdleExecutors(queryType)
	}

	if idleExecutors > 0 {

		priority := router.priorities[queryType]

		index := idleExecutors

		if pendingQueries < idleExecutors {

			index = pendingQueries
		}

		executor := 0

		for index > 0 {

			if router.queuedQueries[queryType][priority].Size() == 0 {

				if priority == MaxPriority-1 {

					priority = 0

					continue
				}

				priority++

				continue
			}

			context, found := router.deque(queryType, priority)

			if found {

				router.sendQuery(context, router.idleExecutors[executor], priority, queryType)

				executor++

				index--
			}

			if index > 0 && priority == int(utils.P0) {

				context, found = router.deque(queryType, priority)

				if found {

					router.sendQuery(context, router.idleExecutors[executor], priority, queryType)

					executor++

					index--
				}
			}

			if priority == MaxPriority-1 {

				priority = 0

				continue
			}

			priority++
		}

		router.priorities[queryType] = priority
	}
}

func (router *RequestRouter) getIdleExecutors(datastoreType query.QueryEngineType) int {

	size := 0

	for executor, available := range router.executors {

		if router.executorAllocations[executor] == datastoreType && available {

			router.idleExecutors[size] = executor

			size++
		}
	}

	return size

}

func (router *RequestRouter) dispatchErrors(err error, context utils.MotadataMap, status utils.QueryStatus) {

	poolIndex, bytes := router.memoryPool.AcquireBlobPool(-1)

	defer router.memoryPool.ReleaseBytePool(poolIndex)

	buffer := bytes2.NewBuffer(bytes[:0])

	codec.EncodeINT64Value(context.GetInt64Value(query.QueryId), buffer)

	codec.EncodeINT64Value(context.GetInt64Value(query.SubQueryId), buffer)

	buffer.WriteByte(byte(100))

	codec.EncodeINT64Value(int64(0), buffer) // total scanned records

	codec.EncodeINT64Value(0, buffer) // total time taken by query executor and worker

	buffer.WriteByte(byte(status))

	errs := utils.CombineErrors([]string{err.Error()})

	codec.EncodeINT32Value(int32(len(errs)), buffer)

	buffer.WriteString(errs)

	if context.Contains(utils.AggregationJobId) && context[utils.AggregationJobId] != nil {

		aggregationJobs[context.GetIntValue(utils.AggregationJobId)].JobQueryAcks <- utils.NotAvailable

	} else {

		responseBuffer := &bytes2.Buffer{}

		//first 2 bytes length
		responseBuffer.WriteByte(byte(len(utils.DatastoreQueryResponse)))

		responseBuffer.WriteByte(byte(len(utils.DatastoreQueryResponse) >> 8))

		responseBuffer.Write([]byte(utils.DatastoreQueryResponse))

		responseBuffer.Write(snappy.Encode(nil, buffer.Bytes()))

		utils.PublisherResponses <- responseBuffer.Bytes()

	}

}

func (router *RequestRouter) qualifyDrillDownEvent(context utils.MotadataMap) {

	queryId := context.GetInt64Value(query.QueryId)

	if _, ok := router.drillDownQueryContexts[queryId]; ok {

		bytes := make([]byte, len(router.drillDownQueryContexts[queryId]))

		copy(bytes, router.drillDownQueryContexts[queryId])

		context[query.DrillDownQueryContext] = bytes

	} else {

		eventId := 0

		for _, value := range router.drillDownQueries {

			if eventId < value {

				eventId = value
			}
		}

		eventId++

		if eventId == utils.DrillDownQueryExecutors*utils.MaxDrillDownEvents {

			eventId = 0
		}

		router.drillDownQueries[queryId] = eventId
	}

	context[query.DrillDownEventId] = router.drillDownQueries[queryId]
}

//new methods for priority queue

func (router *RequestRouter) deque(queryEngineType query.QueryEngineType, priority int) (utils.MotadataMap, bool) {

	if router.queuedQueries[queryEngineType][priority].Size() > 0 {

		iterator := router.queuedQueries[queryEngineType][priority].Iterator()

		iterator.Next()

		context, _ := router.queuedQueries[queryEngineType][priority].Get(iterator.Key())

		router.queuedQueries[queryEngineType][priority].Remove(iterator.Key())

		if utils.TraceEnabled() {

			routerLogger.Trace(fmt.Sprintf("query engine type: %v, priority: %v", queryEngineType, priority))
		}

		return utils.ToMap(context), true
	}

	return nil, false
}

func (router *RequestRouter) sendQuery(context utils.MotadataMap, executor, priority int, queryEngineType query.QueryEngineType) {

	router.executors[executor] = false

	if utils.DebugEnabled() {

		routerLogger.Debug(fmt.Sprintf("query assigned to executor %v", executor))
	}

	if queryEngineType == query.DrillDown {

		router.qualifyDrillDownEvent(context)
	}

	queryId := context.GetInt64Value(query.QueryId)

	subQueryId := context.GetInt64Value(query.SubQueryId)

	if priority == int(utils.P1) { // do not track aggregation query timer..

		executors[executor].Requests <- context

	} else {

		router.queryTrackers[queryId] = time.Now().UnixMilli()

		router.queryEngineTypesByQueryId[queryId] = queryEngineType

		// Start memory tracking for the query
		if utils.GlobalMemoryManager != nil {
			queryIdStr := context.GetStringValue(query.QueryId)
			subQueryIdStr := context.GetStringValue(query.SubQueryId)
			utils.GlobalMemoryManager.StartTracking(queryIdStr, subQueryIdStr)
		}

		executors[executor].Requests <- context

		buffer := &bytes2.Buffer{}

		//first 2 bytes length
		buffer.WriteByte(byte(len(utils.DatastoreQueryAck)))

		buffer.WriteByte(byte(len(utils.DatastoreQueryAck) >> 8))

		buffer.Write([]byte(utils.DatastoreQueryAck))

		codec.EncodeINT64Value(queryId, buffer)

		codec.EncodeINT64Value(subQueryId, buffer)

		utils.PublisherResponses <- buffer.Bytes()
	}
}

func (router *RequestRouter) publishHealthMetrics() {

	defer func() {

		// clean up all the counters

		clear(router.queryLatencies)

		clear(router.queryEngineTypesByQueryId)

		router.abortedQueries = 0

		router.aggregationQueries = 0

		clear(router.queriesByQueryEngineType)
	}()

	metrics := make(map[string]int)

	// Total aborted queries
	metrics[utils.AbortedQueries] = router.abortedQueries

	//Total pending query query engine type wise

	for queryEngineType, queuedQueries := range router.queuedQueries {

		metrics[getPendingQueryMetricByQueryEngineType(queryEngineType)] = getSize(queuedQueries)
	}

	//Total queries
	metrics[utils.AggregationQueries] = router.aggregationQueries

	for _, value := range router.queriesByQueryEngineType {

		metrics[utils.Queries] += value
	}

	queryLatency := 0

	// query latency engine type wise
	for queryEngineType, latency := range router.queryLatencies {

		if _, ok := router.queriesByQueryEngineType[queryEngineType]; !ok {

			metrics[queryLatencyMetricsByQueryEngineType[queryEngineType]] = 0

			continue
		}

		value := int(latency) / router.queriesByQueryEngineType[queryEngineType]

		metrics[queryLatencyMetricsByQueryEngineType[queryEngineType]] = value

		queryLatency += value

	}

	metrics[utils.DiscardedQueries] = router.discardedQueries

	router.discardedQueries = 0

	if queryLatency > 0 {

		queryLatency = queryLatency / len(router.queryLatencies)
	}

	// total query latency
	metrics[utils.QueryLatency] = queryLatency

	if utils.QueryStatsLogging {

		var output string

		for metric, value := range metrics {

			output += fmt.Sprintf("Metric: %v, Value: %v\n", metric, value)
		}

		routerLogger.Info(fmt.Sprintf("Query Stats\n%v", output))

	}

	utils.HealthCheckupJobNotifications <- metrics
}

func (router *RequestRouter) abort() {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			routerLogger.Error(fmt.Sprintf(errorQueryRouter, err))

			routerLogger.Error(fmt.Sprintf(errorStackTraceQueryRouter, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		}
	}()

	for queryId, timestamp := range router.queryTrackers {

		if time.Now().UnixMilli()-timestamp >= router.abortTimerThresholdMillis {

			router.abortedQueries += 1

			router.queryLatencies[router.queryEngineTypesByQueryId[queryId]] += time.Now().UnixMilli() - timestamp

			delete(router.queryTrackers, queryId)

			delete(router.queryEngineTypesByQueryId, queryId)

			buffer := &bytes2.Buffer{}

			buffer.WriteByte(utils.QueryAbort)

			context := make(utils.MotadataMap)

			context[query.QueryId] = queryId

			context[datastore.Status] = utils.Aborted

			bytes, _ := json.Marshal(&context)

			buffer.Write(bytes)

			utils.Requests <- append([]byte{utils.QueryAbort}, buffer.Bytes()...)
		}
	}
}

func (router *RequestRouter) processQueuedQuery(response string, tokenizer *utils.Tokenizer) {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			routerLogger.Error(fmt.Sprintf(errorQueryRouter, err))

			routerLogger.Error(fmt.Sprintf(errorStackTraceQueryRouter, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		}
	}()

	utils.Split(response, utils.KeySeparator, tokenizer)

	if tokenizer.Counts > 1 {

		router.executors[codec.StringToINT(tokenizer.Tokens[1])] = true

		if utils.DebugEnabled() {

			routerLogger.Debug(fmt.Sprintf("executor %v is available...", codec.StringToINT(tokenizer.Tokens[1])))
		}

		queryId := codec.StringToINT64(tokenizer.Tokens[2])

		subQueryId := codec.StringToINT64(tokenizer.Tokens[3])

		if _, ok := router.runningQueries[queryId]; ok {

			delete(router.runningQueries[queryId], subQueryId)

			if len(router.runningQueries[queryId]) == 0 {

				delete(router.runningQueries, queryId)

				if _, ok = router.queryTrackers[queryId]; ok {

					router.queryLatencies[router.queryEngineTypesByQueryId[queryId]] += time.Now().UnixMilli() - router.queryTrackers[queryId]

				}

				// Stop memory tracking for completed query
				if utils.GlobalMemoryManager != nil {
					queryIdStr := codec.INT64ToStringValue(queryId)
					subQueryIdStr := codec.INT64ToStringValue(subQueryId)
					utils.GlobalMemoryManager.StopTracking(queryIdStr, subQueryIdStr)
				}

				// if query is not drill down and all sub queries job finished delete query from query abort time tracker..
				if _, ok = router.drillDownQueries[queryId]; !ok {

					delete(router.queryTrackers, queryId)
				}

				delete(router.queryEngineTypesByQueryId, queryId)
			}
		}

		if utils.DebugEnabled() {

			routerLogger.Debug(fmt.Sprintf("pending queries %v", len(router.runningQueries)))
		}

		queryEngineType := query.QueryEngineType(codec.StringToINT(tokenizer.Tokens[0]))

		if pending := getSize(router.queuedQueries[queryEngineType]); pending > 0 {

			router.processQueuedQueries(queryEngineType, pending, utils.NotAvailable)

		}

	}
}

func getSize(queries []*linkedhashmap.Map) (size int) {

	for index := range queries {

		size += queries[index].Size()

	}

	return
}

func getQueryEngineType(context utils.MotadataMap) (queryEngineType query.QueryEngineType) {

	datastoreType := utils.DatastoreType(context.GetMapValue(query.VisualizationDataSources).GetIntValue(utils.Type))

	category := context.GetStringValue(query.VisualizationCategory)

	if category == query.Anomaly || category == query.Forecast || category == query.Baseline {

		return query.AIOps

	} else if context.Contains(utils.DrillDown) && strings.EqualFold(context.GetStringValue(utils.DrillDown), utils.Yes) {

		if datastoreType == utils.PerformanceMetric || datastoreType == utils.ObjectStatusMetric || datastoreType == utils.TrapFlapHistory || datastoreType == utils.ObjectStatusFlapMetric || datastoreType == utils.NetRouteMetric || datastoreType == utils.NetRouteStatusMetric || datastoreType == utils.SLOMetric {

			return query.Metric
		}

		return query.DrillDown

	} else if datastoreType == utils.PerformanceMetric || datastoreType == utils.ObjectStatusMetric ||
		datastoreType == utils.TrapFlapHistory || datastoreType == utils.NetRouteMetric || datastoreType == utils.ObjectStatusFlapMetric || datastoreType == utils.NetRouteStatusMetric || datastoreType == utils.SLOMetric {

		return query.Metric

	} else if datastoreType == utils.Flow {

		return query.Flow

	}

	return query.Log
}

func getPendingQueryMetricByQueryEngineType(queryEngineType query.QueryEngineType) string {

	switch queryEngineType {

	case query.Log:

		return utils.LogPendingQueries

	case query.Flow:

		return utils.FlowPendingQueries

	case query.Metric:

		return utils.MetricPendingQueries

	case query.DrillDown:

		return utils.DrillDownPendingQueries

	case query.AIOps:

		return utils.AIOpsPendingQueries

	}

	return "log"

}
