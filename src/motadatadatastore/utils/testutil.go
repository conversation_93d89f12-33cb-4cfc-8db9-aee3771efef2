/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-02-04             <PERSON>wap<PERSON>l <PERSON><PERSON> Dave       	MOTADATA-4885 Visualization Time Range Conditions Removed.
* 2025-03-05			 <PERSON><PERSON>l <PERSON>			Motadata-5190 Altered switch case structure to match SonarQube standard
* 2025-03-05             Vedant Dokania          Motadata-5451  Status Flap vertical new datastore type
* 2025-03-21			 Dhaval <PERSON>tad<PERSON>-5452 Resolved Query Type For NetRouteMetric
* 2025-04-02			 Dhaval <PERSON>ra			<PERSON>tad<PERSON>-4859  Added NetRoute Status Metric Datastore Type
* 2025-05-15			 Vedant Dokania			Motadata-6251  Unpacking changes for the testcases
* 2025-06-04             Aashil Shah            MOTADATA-5780 Initialized IOCP Workers and set DataWriterSyncMinTimerSeconds
 */

package utils

import (
	bytes2 "bytes"
	clickhouseContext "context"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/ClickHouse/clickhouse-go/v2/lib/driver"
	"github.com/golang/snappy"
	cp "github.com/otiai10/copy"
	"github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"
	"math"
	"math/rand"
	"os"
	"reflect"
	"strconv"
	"strings"
	"time"
)

const (
	Today = "today" //start of the day to now

	Last24Hours = "last.24.hours"

	Custom1Month = "custom.1.month"

	Custom1Week = "custom.1.week"

	Last6Hours = "last.6.hours"

	CustomFlowTimeline = "custom.flow.timeline"

	Last9Hours = "last.9.hours"

	Last12Hours = "last.12.hours"

	Last1Hour = "last.hour"

	Last5Minutes = "last.5.minutes"

	LastDay = "last.day"

	Last15Minutes = "last.15.minutes"

	Last48Hours = "last.48.hours"

	Last2Days = "last.2.days"

	Last7Days = "last.7.days"

	Last1Month = "last.1.month"

	ThisWeek = "this.week"

	ThisMonth = "this.month"

	LastQuarter = "last.quarter"

	RelativeTimeline = "relative.timeline"

	VisualizationTimeRangeInclusive = "visualization.time.range.inclusive"

	FromDateTime = "from.datetime"

	ToDateTime = "to.datetime"

	Duration = "duration"
)

func GetContext(contexts MotadataMap, keys []string) (result MotadataMap) {

	for _, key := range keys {

		if result == nil {

			result = contexts.GetMapValue(key)

		} else {

			result = result.GetMapValue(key)

		}

	}
	return
}

func InterfaceToINT32Values(values []interface{}) (int32Values []int32) {

	for _, value := range values {

		int32Values = append(int32Values, int32(value.(float64)))
	}

	return
}

func UnmarshalJson(bytes []byte, context MotadataMap) MotadataMap {

	gjson.ParseBytes(bytes).ForEach(func(key, value gjson.Result) bool {
		context[key.String()] = value.Value()
		return true
	})

	return context
}

func InterfaceToFLOAT64Values(values []interface{}) (float64Values []float64) {

	for _, value := range values {

		if reflect.TypeOf(value).Kind().String() == "int64" {

			float64Values = append(float64Values, float64(value.(int64)))
		} else {

			float64Values = append(float64Values, value.(float64))

		}

	}

	return
}

func InterfaceToINT64Values(values []interface{}) (int64Values []int64) {

	for _, value := range values {

		if reflect.TypeOf(value).Kind().String() == "int64" {

			int64Values = append(int64Values, value.(int64))

		} else {

			int64Values = append(int64Values, int64(value.(float64)))
		}
	}

	return
}

func InterfaceToINT8Values(values []interface{}) (int8Values []int8) {

	for _, value := range values {

		int8Values = append(int8Values, int8(value.(float64)))
	}

	return
}

func ToINT64Values(values []interface{}) (int64Values []int64) {

	for _, value := range values {

		int64Values = append(int64Values, value.(int64))
	}

	return
}

func InterfaceToINT16Values(values []interface{}) (int16Values []int16) {

	for _, value := range values {

		int16Values = append(int16Values, int16(value.(float64)))
	}

	return
}

func InterfaceINT64ToStringValues(values []interface{}) (stringValues []string) {

	for _, value := range values {

		stringValues = append(stringValues, strconv.Itoa(int(value.(int64))))
	}

	return
}

func CleanUpStores() {

	_ = os.RemoveAll(CurrentDir + PathSeparator + DatastoreDir + PathSeparator)

	_ = os.RemoveAll(BackUpDir + PathSeparator)

	_ = os.RemoveAll(CurrentDir + PathSeparator + LogDirectory + PathSeparator)

	_ = os.RemoveAll(CurrentDir + PathSeparator + "jobs" + PathSeparator)

	_ = os.RemoveAll(CurrentDir + PathSeparator + ConfigDir + PathSeparator)

	_ = os.RemoveAll(CurrentDir + PathSeparator + StopWordFile)

	_ = os.RemoveAll(EventDir + PathSeparator)

	_ = os.Mkdir(ConfigDir, 0777)

}

func InitDatabases(databases []string, address string, username string, password string) {

	connection, ctx := InitClickHouse(address, username, password)

	for _, database := range databases {

		CleanUpClickHouseDB(connection, ctx, database)
	}

	for _, database := range databases {

		createDatabaseIfNotExists(connection, ctx, database)
	}
}

func CheckProgress(bytes []byte) (bool, *bytes2.Buffer) {

	topicLength := int32(bytes[0]) |
		int32(bytes[1])<<8

	bytes = bytes[2+topicLength:]

	bytes, _ = snappy.Decode(nil, bytes)

	buffer := bytes2.NewBuffer(bytes)

	//queryId

	_ = int(binary.LittleEndian.Uint64(buffer.Next(8)))

	//sub query id

	_ = int(binary.LittleEndian.Uint64(buffer.Next(8)))

	progress, _ := buffer.ReadByte()

	if int(progress) == 100 {

		return true, buffer
	}

	return false, nil
}

/*

	Note :- If the datatype ordinal changes, and tests fail please do the corresponding changes here
	codec package cannot be imported here hence the constants are written explicitly.
*/

func UnpackResponse(buffer *bytes2.Buffer, forecastChartResult bool) (table map[string][]interface{}, errors string, totalRecords int, totalTime int) {

	table = make(map[string][]interface{})

	totalRecords = int(binary.LittleEndian.Uint64(buffer.Next(8)))

	totalTime = int(binary.LittleEndian.Uint64(buffer.Next(8)))

	status, _ := buffer.ReadByte()

	if status == byte(Failed) || status == byte(Aborted) {

		errors = string(buffer.Next(int(binary.LittleEndian.Uint32(buffer.Next(4)))))

		errs := make([]string, 0)

		for _, err := range strings.Split(errors, "\n") {

			if err == "" {

				continue
			}

			if err != "no valid data found for the specified time" {

				errs = append(errs, err)
			}
		}

		errors = CombineErrors(errs)

		if len(buffer.Bytes()) == 0 {

			return nil, errors, 0, 0
		}

	}

	_, _ = buffer.ReadByte() //visualizationTypes

	columnElementSize := int(buffer.Next(1)[0])

	//rowRecords

	rowRecords := int(binary.LittleEndian.Uint32(buffer.Next(4)))

	predictionIndex := int16(NotAvailable)

	if forecastChartResult {

		predictionIndex = int16(binary.LittleEndian.Uint16(buffer.Next(2)))
	}

	// make buffer equivalant to the size of columnElement Size
	buffers := make([]*bytes2.Buffer, columnElementSize)

	for index := 0; index < columnElementSize; index++ {

		buffers[index] = bytes2.NewBuffer(buffer.Next(int(binary.LittleEndian.Uint32(buffer.Next(4))) - 4))
	}

	for index := 0; index < len(buffers); index++ {

		buffer = buffers[index]

		aggregation := int(buffer.Next(1)[0])

		dataType := int(buffer.Next(1)[0])

		header := string(buffer.Next(int(binary.LittleEndian.Uint16(buffer.Next(2)))))

		_ = string(buffer.Next(int(binary.LittleEndian.Uint16(buffer.Next(2)))))

		if aggregation == 6 { //sparkline

			table = unpackSparklineVisualizationResult(header, buffer, dataType, table)

			continue
		}

		if forecastChartResult {

			table = unpackForecastChartVisualizationResult(header, buffer, dataType, table, rowRecords)

			table["prediction.index"] = []interface{}{predictionIndex}

		} else {

			table = unpack(table, dataType, bytes2.NewBuffer(buffer.Bytes()), header, int32(rowRecords))

		}

	}

	return table, errors, totalRecords, totalTime
}

func unpackForecastChartVisualizationResult(header string, buffer *bytes2.Buffer, dataType int, table map[string][]interface{}, records int) map[string][]interface{} {

	switch dataType {

	case 112:

		for i := 0; i < records; i++ {

			table[header] = append(table[header], int64(binary.LittleEndian.Uint64(buffer.Next(8))))
		}

	case 160:

		for i := 0; i < records; i++ {

			table[header] = append(table[header], math.Float64frombits(binary.LittleEndian.Uint64(buffer.Next(8))))

		}
	}

	return table
}

func unpackSparklineVisualizationResult(name string, buffer *bytes2.Buffer, dataType int, table map[string][]interface{}) map[string][]interface{} {

	objectId := 0

	ticks := int(binary.LittleEndian.Uint16(buffer.Next(2)))

	for buffer.Len() != 0 {

		key := strconv.Itoa(objectId) + KeySeparator + name

		tickBuffer := bytes2.NewBuffer(buffer.Next(8 * ticks))

		valueBuffer := bytes2.NewBuffer(buffer.Next(getBytes(dataType) * ticks))

		for tickBuffer.Len() != 0 {

			table[key+DotSeparator+"time"] = append(table[key+DotSeparator+"time"], int64(binary.LittleEndian.Uint64(tickBuffer.Next(8))))
		}

		// in case of sparkline we need to unpack till the length of ticks
		table = unpack(table, dataType, valueBuffer, key, int32(ticks))

		objectId++

	}
	return table
}

func unpack(table map[string][]interface{}, dataType int, buffer *bytes2.Buffer, columnName string, rowRecords int32) map[string][]interface{} {

	records := int32(0)

	passOver := false

start:

	for buffer.Len() != 0 || passOver {

		switch dataType {

		case 48:

			table[columnName] = append(table[columnName], int32(binary.LittleEndian.Uint32(buffer.Next(4))))

			records++

		case 112:

			/*
				for queries served from aggregation the monitor resolved is int64
			*/

			if columnName == "monitor" {

				table[columnName] = append(table[columnName], int32(binary.LittleEndian.Uint64(buffer.Next(8))))

			} else {

				table[columnName] = append(table[columnName], int64(binary.LittleEndian.Uint64(buffer.Next(8))))
			}

			records++

		case 160:

			table[columnName] = append(table[columnName], math.Round(math.Float64frombits(binary.LittleEndian.Uint64(buffer.Next(8)))*100)/100)

			records++

		case 176:

			/*
				for raw queries the monitor is string
			*/

			if columnName == "monitor" {

				length := int(binary.LittleEndian.Uint32(buffer.Next(4)))

				table[columnName] = append(table[columnName], StringToInt32(string(buffer.Next(length))))

			} else {

				length := int(binary.LittleEndian.Uint32(buffer.Next(4)))

				table[columnName] = append(table[columnName], string(buffer.Next(length)))
			}

			records++

		}
	}

	// in case of records are not equall to total records we try to read more records same as JAVA and which will encounter a error in testcases

	if records != rowRecords {

		passOver = true

		goto start
	}

	return table
}

func StringToInt32(value string) int32 {

	result, _ := strconv.ParseInt(strings.TrimSpace(value), 10, 32)

	return int32(result)

}

func getBytes(dataType int) int {

	switch dataType {

	case 160:

		return 8

	case 112:

		return 8

	case 48:

		return 4

	}

	return 0
}

func UpdateConfigs(bytes []byte, modifiedConfigs MotadataMap) []byte {

	os.MkdirAll(CurrentDir+PathSeparator+ConfigDir, 0755)

	motadataConfigBytes, _ := json.Marshal(MotadataMap{
		"manager.id":        "dummy_testing_id",
		"installation.mode": "PRIMARY",
	})

	os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+MotadataDataConfigFile, motadataConfigBytes, 0755)

	var configs MotadataMap

	err := json.Unmarshal(bytes, &configs)

	panicError(err)

	configs["datastore.metric.query.executors"] = 3

	configs["datastore.log.query.executors"] = 3

	configs["datastore.max.historical.records"] = 300000

	configs["datastore.flow.query.executors"] = 3

	configs["system.log.level"] = 2

	configs["datastore.iocp.workers"] = 16

	//change parameter according to the need
	configs["enable.aggregation"] = Yes

	if EnvironmentType != DatastoreBenchIntegrationEnvironment && EnvironmentType != DatastoreBenchUnitEnvironment {

		configs["datastore.env.type"] = DatastoreTestEnvironment

		EnvironmentType = DatastoreTestEnvironment
	}

	MaxBarChartTicks = 200

	for key, value := range modifiedConfigs {

		configs[key] = value
	}

	bytes, err = json.Marshal(configs)

	panicError(err)

	return bytes

}

// make clickhouse connection and database
func InitClickHouse(address string, username string, password string) (driver.Conn, clickhouseContext.Context) {

	var err error

	connection, err := clickhouse.Open(&clickhouse.Options{

		Addr: []string{address},

		Auth: clickhouse.Auth{

			Database: "default",

			Username: username,

			Password: password,
		},
	})

	panicError(err)

	ctx := clickhouse.Context(clickhouseContext.Background(), clickhouse.WithSettings(clickhouse.Settings{

		"max_block_size": 10,
	}))

	return connection, ctx
}

// drop database and create new database at the starting of the insertion
func CleanUpClickHouseDB(connection clickhouse.Conn, ctx clickhouseContext.Context, database string) {

	var err error

	err = connection.Exec(ctx, "drop database "+database)

	if err != nil {

		if !(strings.Contains(err.Error(), "doesn't exist") || strings.Contains(err.Error(), "does not exist")) {

			panic(err)
		}
	}

	err = connection.Exec(ctx, "create database "+database)

	panicError(err)

}

// create new database
func createDatabaseIfNotExists(connection driver.Conn, ctx clickhouseContext.Context, database string) {

	err := connection.Exec(ctx, "create database "+database)

	if err != nil && !strings.Contains(err.Error(), "already exists") {

		panic(err)

	}

}

// create clickhouse tables , tables are sent in a list of strings
func CreateClickHouseTables(connection clickhouse.Conn, ctx clickhouseContext.Context, tables []string) {

	for _, table := range tables {

		_ = connection.Exec(ctx, table)
	}

}

func CloseClickHouseDBConnection(connection clickhouse.Conn, ctx clickhouseContext.Context) {

	ctx.Done()

	err := connection.Close()

	panicError(err)

}

func GetTimeline(timeline string) (result MotadataMap) {

	result = make(MotadataMap)

	currentTime := time.Now().UTC()

	currentHour := currentTime.Hour()

	currentSecond := 0

	currentMinute := currentTime.Minute()

	switch {

	case timeline == LastDay:

		result[RelativeTimeline] = "-1d"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-1, 0, 0, 0, 0, time.UTC).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-1, 23, 59, 59, 0, time.UTC).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case timeline == Custom1Month:

		result[RelativeTimeline] = "-30d"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-30, 0, 0, 0, 0, time.UTC).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 23, 59, 59, 0, time.UTC).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case timeline == Custom1Week:

		result[RelativeTimeline] = "-7d"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-7, 0, 0, 0, 0, time.UTC).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 23, 59, 59, 0, time.UTC).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case timeline == Last5Minutes:

		result[RelativeTimeline] = "-5m"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute-5, currentSecond, 0, time.UTC).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case timeline == Last15Minutes:

		result[RelativeTimeline] = "-15m"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute-15, currentSecond, 0, time.UTC).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case timeline == Last1Hour:

		result[RelativeTimeline] = "-1h"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour-1, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case timeline == Last6Hours || timeline == CustomFlowTimeline:

		result[RelativeTimeline] = "-6h"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour-6, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case timeline == Last9Hours:

		result[RelativeTimeline] = "-9h"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour-9, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case timeline == Last12Hours:

		result[RelativeTimeline] = "-12h"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour-12, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case timeline == Today:

		result[RelativeTimeline] = "today"

		fromTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, time.UTC)

		result[FromDateTime] = fromTime.UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

		if result.GetIntValue(Duration) < 300 {

			fromTime.Add(-3600 * time.Second)

			result[FromDateTime] = fromTime.UnixMilli()

			result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000
		}

	case timeline == Last24Hours:

		result[RelativeTimeline] = "-24H"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour-24, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case timeline == Last48Hours:

		result[RelativeTimeline] = "-48H"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour-48, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case timeline == Last2Days:

		result[RelativeTimeline] = "-2D"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-2, currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case timeline == Last7Days:

		result[RelativeTimeline] = "-7d"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-7, currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case timeline == Last1Month:

		result[RelativeTimeline] = "-30d"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-30, currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case timeline == ThisMonth:

		result[RelativeTimeline] = "-30d"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), 1, 0, 0, 0, 0, time.UTC).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case timeline == ThisWeek:

		result[RelativeTimeline] = "-7d"

		weekDay := currentTime.Weekday().String()

		currentWeekDay := getCurrentWeekDay(weekDay)

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-currentWeekDay, 0, 0, 0, 0, time.UTC).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	case timeline == LastQuarter:

		result[RelativeTimeline] = "-90d"

		result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-90, currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), currentHour, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

		result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	}

	return
}

func GetRollingWindowTimeline(days, rollingStartWindow, rollingEndWindow, minute int) (MotadataMap, string) {

	result := make(MotadataMap)

	currentTime := time.Now().UTC()

	currentSecond := 0

	currentMinute := currentTime.Minute()

	if minute != NotAvailable {

		currentMinute = minute
	}

	result[FromDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-days, rollingStartWindow, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

	result[ToDateTime] = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), rollingEndWindow, currentMinute, currentSecond, 0, time.UTC).UnixMilli()

	result[Duration] = (result.GetInt64Value(ToDateTime) - result.GetInt64Value(FromDateTime)) / 1000

	result["time.window.check"] = Yes

	clickHouseQuery := Empty

	for day := days; day >= 0; day-- {
		if strings.EqualFold(clickHouseQuery, Empty) {
			obj := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-day, rollingStartWindow, currentMinute, currentSecond, 0, time.UTC).UnixMilli()
			clickHouseQuery = "(( timestamp >= " + INT64ToStringValue(time.UnixMilli(obj).Unix()) + SpaceSeparator

			obj = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-day, rollingEndWindow, currentMinute, currentSecond, 0, time.UTC).UnixMilli()
			clickHouseQuery += "and timestamp <= " + INT64ToStringValue(time.UnixMilli(obj).Unix()) + SpaceSeparator + ")" + SpaceSeparator

			if day-1 != 0 || days == 1 {
				clickHouseQuery += "or" + SpaceSeparator
			}
		} else {
			obj := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-day, rollingStartWindow, currentMinute, currentSecond, 0, time.UTC).UnixMilli()
			clickHouseQuery += "( timestamp >= " + INT64ToStringValue(time.UnixMilli(obj).Unix()) + SpaceSeparator

			obj = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-day, rollingEndWindow, currentMinute, currentSecond, 0, time.UTC).UnixMilli()
			if day-1 != -1 {
				clickHouseQuery += "and timestamp <= " + INT64ToStringValue(time.UnixMilli(obj).Unix()) + SpaceSeparator + ")" + SpaceSeparator
				clickHouseQuery += "or" + SpaceSeparator
			} else {
				clickHouseQuery += "and timestamp < " + INT64ToStringValue(time.UnixMilli(obj).Unix()) + SpaceSeparator + "))" + SpaceSeparator
			}
		}
	}

	return result, clickHouseQuery
}

func ToINT32Values(values []interface{}) (int32Values []int32) {

	for _, value := range values {

		int32Values = append(int32Values, value.(int32))
	}

	return
}

func ReadLogFile(component string, directory string) ([]byte, error) {

	logDirectory := CurrentDir + PathSeparator + LogDirectory + PathSeparator + directory + PathSeparator

	timestamp := time.Now().Format(LogFileFormat)

	logFile := logDirectory + strings.ReplaceAll(LogFile, "@@@", timestamp)

	bytes, err := os.ReadFile(strings.ReplaceAll(logFile, "###", component))

	if err != nil {

		timestamp = time.Now().Add(-time.Minute * 10).Format(LogFileFormat)

		logFile = logDirectory + strings.ReplaceAll(LogFile, "@@@", timestamp)

		return os.ReadFile(strings.ReplaceAll(logFile, "###", component))
	}

	timestamp = time.Now().Add(time.Minute * 10).Format(LogFileFormat)

	logFile = logDirectory + strings.ReplaceAll(LogFile, "@@@", timestamp)

	bufferBytes, err := os.ReadFile(strings.ReplaceAll(logFile, "###", component))

	if err != nil {

		bytes = append(bytes, bufferBytes...)
	}

	if len(bufferBytes) == 0 {

		err = nil
	}

	return bytes, err

}

func ResolveQueryType(queryType string) int {

	queryType = strings.ToLower(queryType)

	datastoreType := None

	switch queryType {

	case "log":
		datastoreType = Log

	case "flow":

		datastoreType = Flow

	case "trap":

		datastoreType = Trap

	case "audit":

		datastoreType = Audit

	case "notification":
		datastoreType = Notification

	case "correlated.metric":
		datastoreType = CorrelationMetric

	case "runbook.worklog":
		datastoreType = RunbookWorklog

	case "index":
		datastoreType = Index

	case "policy.result":
		datastoreType = PolicyResult

	case "performance.metric":
		datastoreType = PerformanceMetric

	case "status.metric":
		datastoreType = ObjectStatusMetric

	case "policy.duration.metric":
		datastoreType = PolicyFlapHistory

	case "status.duration.metric":
		datastoreType = StatusFlapHistory

	case "metric.policy":
		datastoreType = MetricPolicy

	case "event.policy":
		datastoreType = EventPolicy

	case "metric":
		datastoreType = PerformanceMetric

	case "availability":
		datastoreType = ObjectStatusMetric

	case "status.flap":
		datastoreType = StatusFlapHistory

	case "policy.flap":
		datastoreType = PolicyFlapHistory

	case "trap.flap":
		datastoreType = TrapFlapHistory
	case "health":
		datastoreType = HealthMetric

	case "config.history":
		datastoreType = ConfigHistory

	case "compliance":
		datastoreType = Compliance

	case "netroute.metric":

		datastoreType = NetRouteMetric

	case "status.flap.metric":
		datastoreType = ObjectStatusFlapMetric

	case "netroute.status.metric":

		datastoreType = NetRouteStatusMetric

	case "slo.metric":
		datastoreType = SLOMetric

	}

	return int(datastoreType)

}

func CopyBatch(destinationBatch MotadataMap, sourceBatch MotadataMap) {

	for key, value := range sourceBatch {

		if reflect.ValueOf(value).String() == "<map[string][]int32 Value>" {

			values := make(map[string][]int32)

			for key1, value1 := range value.(map[string][]int32) {

				values[key1] = value1
			}

			destinationBatch[key] = values

		} else if reflect.ValueOf(value).String() == "<map[string][]int64 Value>" {

			values := make(map[string][]int64)

			for key1, value1 := range value.(map[string][]int64) {

				values[key1] = value1
			}

			destinationBatch[key] = values
		} else if reflect.ValueOf(value).String() == "<map[string][]string Value>" {

			values := make(map[string][]string)

			for key1, value1 := range value.(map[string][]string) {

				values[key1] = value1
			}

			destinationBatch[key] = values

		} else {

			destinationBatch[key] = value
		}

	}
}

func GenerateRandomString(length int) string {

	var chars = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0987654321")

	runes := make([]rune, length)

	for i := range runes {

		runes[i] = chars[rand.Intn(len(chars))]

	}

	return string(runes)

}

func FLOAT64ToStringValue(value float64) (result string) {

	result = fmt.Sprintf("%.2f", value)

	return

}

func ToFlOAT(value interface{}) (result float64) {

	name := reflect.TypeOf(value).Name()

	if name == "int" {

		result = ToFixed(float64(value.(int)))

	} else if name == "int8" {

		result = ToFixed(float64(value.(int8)))

	} else if name == "int64" {

		result = ToFixed(float64(value.(int64)))

	} else if name == "int32" {

		result = ToFixed(float64(value.(int32)))

	} else if name == "uint32" {

		result = ToFixed(float64(value.(uint32)))

	} else if name == "uint64" {

		result = ToFixed(float64(value.(uint64)))

	} else if name == "float64" {

		result = ToFixed(value.(float64))

	}

	return
}

func ToFixed(num float64) float64 {

	return math.Round(num*100) / 100

}

func Create(dir string) {

	_, err := os.Stat(dir)

	if os.IsNotExist(err) {

		_ = os.MkdirAll(dir, 0755)

	}

}

func INT64ToStringValue(value int64) (result string) {

	result = strconv.Itoa(int(value))

	return

}

func InitTestConfigs() {

	if EnvironmentType == DatastoreBenchIntegrationEnvironment {

		DiskIOWorkers = 90

		IndexWriters = 5

		StoreSyncJobs = 8

		MetricAggregators = 5

		VerticalWriters = 5

		HorizontalWriters = 5

		DatastoreFlushTimerSeconds = 5

		DatastoreBrokerWriterFlushTimerSeconds = 5

		VerticalAggregationSyncTimerSeconds = 2

		DataAggregatorSyncTimerSeconds = 5

		DataWriterSyncMaxTimerSeconds = 7

		DataWriterSyncMinTimerSeconds = 7

		EventAggregators = 3

		DataAggregators = 3

		DataWriters = 7

		return
	}

	DiskIOWorkers = 96

	IndexWriters = 13

	StoreSyncJobs = 10

	MetricAggregators = 7

	StaticMetricWriters = 8

	VerticalWriters = 25

	HorizontalWriters = 15

	HealthMetricWriters = 4

	DatastoreFlushTimerSeconds = 5

	DatastoreBrokerWriterFlushTimerSeconds = 5

	VerticalAggregationSyncTimerSeconds = 2

	DataAggregatorSyncTimerSeconds = 5

	DataWriterSyncMaxTimerSeconds = 7

	DataWriterSyncMinTimerSeconds = 7

	EventAggregators = 8

	DataAggregators = 7

	DataWriters = 7

}

func AssertLogMessage(assertions *assert.Assertions, component string, directory string, message string) {

	bytes, err := ReadLogFile(component, directory)

	assertions.Nil(err)

	assertions.Contains(string(bytes), message)
}

func AssertLogMessageInverse(assertions *assert.Assertions, component string, directory string, message string) {

	bytes, _ := ReadLogFile(component, directory)

	assertions.True(true)

	if bytes != nil && len(bytes) > 0 {

		assertions.True(!strings.Contains(string(bytes), message))
	}

}

func panicError(err error) {

	if err != nil {

		panic(err)
	}
}

func InitTestSetup() {

	motadataConfigBytes, _ := json.Marshal(MotadataMap{
		"manager.id":        "dummy_testing_id",
		"installation.mode": "PRIMARY",
	})

	os.MkdirAll(CurrentDir+PathSeparator+ConfigDir, 0755)

	os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+MotadataDataConfigFile, motadataConfigBytes, 0755)

}

func SkipBenchmarkTest() bool {

	for _, arg := range os.Args {

		if strings.Contains(arg, "bench") {

			fmt.Println("skipped")

			return true
		}
	}

	return false
}

func AlterConfigs(config string, value interface{}) {

	configs[config] = value

}

// BackupFailingTestEnvironment backups the datastore, config, jobs and
// pretty much everything that makes up the test environment to run the tests of that component
// This will be then brought back to the local environment for debugging purpose
func BackupFailingTestEnvironment(srcPath, destPath, component string) {

	destinationPath := destPath + PathSeparator + "failing-test-envs" + PathSeparator + component + HyphenSeparator + time.Now().String()

	_ = os.MkdirAll(destinationPath, 0755)

	_ = cp.Copy(srcPath+PathSeparator+DatastoreDir, destinationPath+PathSeparator+DatastoreDir)

	_ = cp.Copy(srcPath+PathSeparator+ConfigDir, destinationPath+PathSeparator+ConfigDir)

	_ = cp.Copy(srcPath+PathSeparator+"jobs", destinationPath+PathSeparator+"jobs")

	_ = cp.Copy(srcPath+PathSeparator+LogDirectory, destinationPath+PathSeparator+LogDirectory)

	_ = cp.Copy(srcPath+PathSeparator+"datastore-events", destinationPath+PathSeparator+"datastore-events")
}
