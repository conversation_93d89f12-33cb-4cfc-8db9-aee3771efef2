/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-02-10			 Dhaval <PERSON>ra			Motad<PERSON>-4913  Added New Modulo Function
* 2025-03-05             Vedant Dokania          Motadata-5451  Status Flap vertical new datastore type
* 2025-03-21			 Dhaval <PERSON>ra			<PERSON>-5452  Added NetRoute Backup Profile
* 2025-04-02			 Dhaval <PERSON>ra	         <PERSON>-4859  Added NetRoute Status Metric Datastore Type
* 2025-04-21             Vedant Dokan<PERSON>         Motadata-4859  Refactored method name StringToBytesReadonly and changed logic of string to byte and reverse conversion
* 2025-05-07             <PERSON><PERSON><PERSON>-6073  Added RedirectStderr function to redirect stderr to log file
* 2025-06-03			 Dhaval Bera			Motadata-6393  Updated With Master Branch
* 2025-06-23             Vedant Dokania         Motadata-6370 Added missing escape characters
* 2025-06-04             Aashil Shah            MOTADATA-5780 Called custom MmapAnonymous and Munmap functions
 */

package utils

import (
	"archive/zip"
	bytes2 "bytes"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-faster/city"
	"github.com/golang/snappy"
	cmap "github.com/orcaman/concurrent-map"
	cp "github.com/otiai10/copy"
	"io"
	"io/fs"
	"net"
	"os"
	"path/filepath"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"sync/atomic"
	"time"
	"unicode"
	"unsafe"
)

type MotadataMap map[string]interface{}

type MotadataStringMap map[string]string

type TxnEntry struct {
	Length int

	Offset int
}

var (
	escapeCharacters = strings.NewReplacer("\"", "\\\"", "\\", "\\\\")

	escapeCharacters1 = strings.NewReplacer("(", "\\(", ")", "\\)", "[", "\\[", "]", "\\]", ".", "\\.", "?", "\\?", "*", "\\*", "`", "\\`", "$", "\\$", "+", "\\+")

	id = int64(0)
)

const (
	interfaceMap = "map[string]interface {}"

	motadataMap = "MotadataMap"
)

///////////////////////////////motadata string map utils methods/////////////////////////

func (context MotadataStringMap) Contains(key string) (result bool) {

	if _, found := context[key]; found {

		return true

	}

	return
}

func (context MotadataStringMap) NotContains(key string) (result bool) {

	if _, found := context[key]; !found {

		return true

	}

	return
}

func (context MotadataStringMap) IsNotEmpty() (result bool) {

	if context != nil && len(context) > 0 {

		return true

	}

	return
}

////////////////////////////// motadata map util methods....///////////////////////////

func (context MotadataMap) Contains(key string) (result bool) {

	if _, found := context[key]; found {

		return true

	}

	return
}

func (context MotadataMap) Clear() {

	for key := range context {

		context.Delete(key)
	}

}

func (context MotadataMap) Delete(key string) {

	if context.Contains(key) {

		delete(context, key)
	}
}

func (context MotadataMap) GetStringValue(key string) (result string) {

	if context.Contains(key) {

		value := context[key]

		name := reflect.TypeOf(value).Name()

		if name == "string" {

			result = value.(string)

		} else if name == "int" {

			result = strconv.Itoa(value.(int))

		} else if name == "int8" {

			result = strconv.Itoa(int(value.(int8)))

		} else if name == "int16" {

			result = strconv.Itoa(int(value.(int16)))

		} else if name == "int32" {

			result = strconv.Itoa(int(value.(int32)))

		} else if name == "int64" {

			result = strconv.FormatInt(value.(int64), 10)

		} else if name == "float64" {

			result = strconv.Itoa(int(value.(float64)))

		}
	}

	return
}

func (context MotadataMap) GetIntValue(key string) (result int) {

	if context.Contains(key) {

		value := context[key]

		name := reflect.TypeOf(value).Name()

		if name == "string" {

			output, _ := strconv.ParseInt(strings.TrimSpace(value.(string)), 10, 64)

			result = int(output)

		} else if name == "uint8" {

			result = int(value.(uint8))

		} else if name == "int" {

			result = value.(int)

		} else if name == "int8" {

			result = int(value.(int8))

		} else if name == "int16" {

			result = int(value.(int16))

		} else if name == "int32" {

			result = int(value.(int32))

		} else if name == "int64" {

			result = int(value.(int64))

		} else if name == "float64" {

			result = int(value.(float64))
		}
	}

	return
}

func (context MotadataMap) GetInt32Value(key string) (result int32) {

	if context.Contains(key) {

		value := context[key]

		name := reflect.TypeOf(value).Name()

		if name == "string" {

			output, _ := strconv.ParseInt(strings.TrimSpace(value.(string)), 10, 32)

			result = int32(output)

		} else if name == "int" {

			result = int32(value.(int))

		} else if name == "int32" {

			result = value.(int32)

		} else if name == "float64" {

			result = int32(value.(float64))

		} else if name == "float32" {

			result = int32(value.(float32))

		}
	}

	return
}

func (context MotadataMap) GetFloatValue(key string) (result float64) {

	if context.Contains(key) {

		value := context[key]

		name := reflect.TypeOf(value).Name()

		if name == "float64" {

			result = value.(float64)

		} else if name == "float32" {

			result = float64(value.(float32))
		} else {

			result = float64(context.GetIntValue(key))
		}
	}

	return

}

func (context MotadataMap) GetValue(key string) (result interface{}) {

	if context.Contains(key) {
		return context[key]
	}
	return
}

func (context MotadataMap) GetInt64Value(key string) (result int64) {

	if context.Contains(key) {

		value := context[key]

		name := reflect.TypeOf(value).Name()

		if name == "string" {

			output, _ := strconv.ParseFloat(strings.TrimSpace(value.(string)), 64)

			result = int64(output)

		} else if name == "int" {

			result = int64(value.(int))

		} else if name == "int64" {

			result = value.(int64)

		} else if name == "int32" {

			result = int64(value.(int32))

		} else if name == "float64" {

			result = int64(value.(float64))
		}
	}

	return
}

func (context MotadataMap) GetUInt64Value(key string) (result uint64) {

	if context.Contains(key) {

		value := context[key]

		name := reflect.TypeOf(value).Name()

		if name == "string" {

			output, _ := strconv.ParseFloat(strings.TrimSpace(value.(string)), 64)

			result = uint64(output)

		} else if name == "int" {

			result = uint64(value.(int))

		} else if name == "uint64" {

			result = value.(uint64)

		} else if name == "int64" {

			result = uint64(value.(int64))

		} else if name == "float64" {

			result = uint64(int64(value.(float64)))
		}
	}

	return
}

func (context MotadataMap) GetMapListValue(key string) (result []MotadataMap) {

	if context.Contains(key) {

		for _, value := range context.GetSliceValue(key) {

			result = append(result, value.(map[string]interface{}))
		}
	}

	return
}

// DeepClone creates a deep copy of a MotadataMap.
//
// Unlike a shallow copy (Clone), DeepClone recursively copies all nested maps,
// ensuring that modifications to the cloned map don't affect the original map
// and vice versa. This is crucial for maintaining data integrity when maps are
// passed between components or stored for later use.
//
// The method handles two types of nested maps:
// 1. MotadataMap: Our custom map type
// 2. map[string]interface{}: Standard Go map type
//
// Non-map values are copied by reference, which is safe for immutable types
// like strings, numbers, and booleans, but may cause issues with mutable types
// like slices or custom structs. Use with caution when the map contains mutable values.
//
// Returns:
//   - A new MotadataMap with all nested maps deeply copied
func (context MotadataMap) DeepClone() MotadataMap {
	// Create a new map to hold the cloned values
	result := make(MotadataMap)

	// Iterate through all key-value pairs in the original map
	for key, value := range context {
		// Check if the value is a map that needs deep cloning
		// We need to handle both MotadataMap and map[string]interface{} types
		if reflect.TypeOf(value).String() == interfaceMap || reflect.TypeOf(value).Name() == motadataMap {
			// For nested maps, convert to MotadataMap if needed and recursively clone
			// This ensures that all levels of nesting are properly copied
			result[key] = ToMap(value).DeepClone()
		} else {
			// For non-map values, copy by reference
			// This is safe for immutable types like strings, numbers, and booleans
			result[key] = value
		}
	}

	return result
}

// Clone creates a shallow copy of a MotadataMap.
//
// A shallow copy duplicates the top-level key-value pairs but shares the same
// underlying values. This means that modifications to nested maps or mutable values
// in the cloned map will affect the original map and vice versa.
//
// Use Clone when:
// - You only need to modify top-level keys
// - Performance is critical and you understand the sharing implications
// - The map contains only immutable values
//
// Use DeepClone instead when:
// - You need to modify nested maps
// - You need complete isolation from the original map
// - The map will be stored or passed to other components
//
// Returns:
//   - A new MotadataMap with the same key-value pairs as the original
func (context MotadataMap) Clone() MotadataMap {
	// Create a new map to hold the cloned values
	result := make(MotadataMap)

	// Copy all key-value pairs by reference
	// This is much faster than deep cloning but doesn't isolate nested structures
	for key, value := range context {
		result[key] = value
	}

	return result
}

// ToMap converts an interface{} to a MotadataMap.
//
// This utility function handles two common cases:
// 1. The value is already a MotadataMap
// 2. The value is a standard map[string]interface{}
//
// This function is particularly useful when working with JSON data or
// other sources where the exact map type isn't known at compile time.
//
// Parameters:
//   - value: The interface{} to convert to a MotadataMap
//
// Returns:
//   - A MotadataMap representation of the input value
//   - An empty map if the input is nil or not a compatible map type
func ToMap(value interface{}) (result MotadataMap) {
	// Check if the value is nil before attempting conversion
	if value != nil {
		// Check if the value is already a MotadataMap
		if reflect.TypeOf(value).Name() == motadataMap {
			// Simple type assertion for MotadataMap
			result = value.(MotadataMap)
		} else if reflect.TypeOf(value).String() == interfaceMap {
			// Convert standard map[string]interface{} to MotadataMap
			// This is a shallow conversion - nested maps remain as map[string]interface{}
			result = value.(map[string]interface{})
		}
	}

	return
}

func (context MotadataMap) GetMapValue(key string) (result MotadataMap) {

	if context.Contains(key) {

		if reflect.TypeOf(context[key]).Name() == motadataMap {

			result = context[key].(MotadataMap)

		} else if reflect.TypeOf(context[key]).String() == interfaceMap {

			result = context[key].(map[string]interface{})
		}

	}

	return
}

func (context MotadataMap) ToConcurrentMap() (result cmap.ConcurrentMap) {

	result = cmap.New()

	for key, value := range context {

		result.Set(key, value)
	}

	return
}

func (context MotadataMap) GetListValue(key string) (values []interface{}) {

	if context.Contains(key) {

		for _, value := range context.GetSliceValue(key) {

			values = append(values, value)
		}
	}

	return
}

func (context MotadataMap) GetSliceValue(key string) (values []interface{}) {

	if context.Contains(key) {

		return context[key].([]interface{})
	}

	return
}

func (context MotadataMap) GetMapKeys() (values []string) {

	for key := range context {

		values = append(values, key)
	}

	return
}

func (context MotadataMap) GetMapValues() (values []interface{}) {

	for _, value := range context {

		values = append(values, value)
	}

	return
}

func (context MotadataMap) GetSliceStringValue(key string) (values []string) {

	if context.Contains(key) {

		return context[key].([]string)
	}

	return
}

func (context MotadataMap) GetSliceInt32Value(key string) (values []int32) {

	if context.Contains(key) {

		return context[key].([]int32)
	}

	return
}

func (context MotadataMap) IsNotEmpty() bool {

	if context != nil && len(context) > 0 {

		return true
	}

	return false
}

func GetStringValue(value interface{}) (result string) {

	if name := reflect.TypeOf(value).Name(); name != Empty {

		if name == "string" {

			result = value.(string)

		} else if name == "int" {

			result = strconv.Itoa(value.(int))

		} else if name == "int8" {

			result = strconv.Itoa(int(value.(int8)))

		} else if name == "int64" {

			result = strconv.Itoa(int(value.(int64)))

		} else if name == "int32" {

			result = strconv.Itoa(int(value.(int32)))

		} else if name == "float64" {

			result = fmt.Sprintf("%.2f", value.(float64))

		} else if name == "float32" {

			result = fmt.Sprintf("%.2f", value.(float32))

		} else if name == "bool" {

			result = strconv.FormatBool(value.(bool))
		}

	} else if name = reflect.TypeOf(value).String(); name != Empty {

		if name == "[]uint8" {

			result = string(value.([]uint8))

		} else if name == "[]byte" {

			result = string(value.([]byte))

		}
	}

	return
}

func removeListDuplicateValues(elements []interface{}) []interface{} {

	keys := make(map[interface{}]bool)

	var values []interface{}

	for _, element := range elements {

		if _, found := keys[element]; !found {

			keys[element] = true

			values = append(values, element)
		}
	}

	return values
}

func ToStringList(elements []interface{}) []string {

	var values []string

	if elements != nil && len(elements) > 0 {

		values = make([]string, len(elements))

		for i := range elements {

			values[i] = elements[i].(string)

		}
	}

	return values
}

func FilterEscapeCharacters(value string) string {

	return escapeCharacters.Replace(value)

}

func FilterEscapeCharacters1(value string) string {

	return escapeCharacters1.Replace(value)

}

//////////////////// Misc  Methods ////////////////////

func CombineErrors(errs []string) string {

	err := Empty

	for index := range errs {

		err += errs[index] + NewLineSeparator
	}

	return err
}

func GetHash64(bytes []byte) uint64 {

	return city.Hash64(bytes)
}

func PrependByte(bytes []byte, byteValue byte) []byte {

	bytes = append(bytes, 0)

	copy(bytes[1:], bytes)

	bytes[0] = byteValue

	return bytes
}

func PrependBytes(bytes []byte, bufferBytes []byte) []byte {

	for i := 0; i < len(bufferBytes); i++ {

		bytes = append(bytes, 0)
	}

	copy(bytes[len(bufferBytes):], bytes)

	for i := 0; i < len(bufferBytes); i++ {

		bytes[i] = bufferBytes[i]
	}

	return bytes
}

func GetStringSliceSize(values []string) int {

	size := 0

	for _, value := range values {

		size += len(value)
	}

	return size
}

func GetINT8BitmapMapSize(bitmaps map[int8][]byte) int {

	size := 0

	for _, value := range bitmaps {

		size += len(value)

		size++
	}

	return size
}

func GetINT16BitmapMapSize(bitmaps map[int16][]byte) int {

	size := 0

	for _, value := range bitmaps {

		size += len(value)

		size += 2
	}

	return size
}

func GetINT32BitmapMapSize(bitmaps map[int32][]byte) int {

	size := 0

	for _, value := range bitmaps {

		size += len(value)

		size += 4
	}

	return size
}

func GetStringBitmapMapSize(bitmaps map[string][]byte) int {

	size := 0

	for key, value := range bitmaps {

		size += len(value)

		size += len(key)
	}

	return size
}

func Reverse(bytes []byte) []byte {

	bufferBytes := make([]byte, len(bytes))

	for i, j := 0, len(bytes)-1; i <= j; i, j = i+1, j-1 {
		bufferBytes[i], bufferBytes[j] = bytes[j], bytes[i]
	}

	return bufferBytes
}

func GetEventId(tick int32, part uint16, position int) int64 {

	id := int(tick) * 1000

	id += int(part)

	id *= 100000

	id += position

	return int64(id)
}

func GetAggregationEventId(tick int32, part uint16) int64 {

	id := int(tick) * 1000

	id += int(part)

	return int64(id)

}

func SplitAggregationEventId(id int64) string {

	part := id % 1000

	id = id / 1000

	return strconv.Itoa(int(id)) + KeySeparator + strconv.Itoa(int(part))

}

func GenerateQueryId() int64 {

	return atomic.AddInt64(&id, 1)
}

/////////  tokenizer related stuff.../////////

func Split(value, delimiter string, tokenizer *Tokenizer) {

	tokenizer.startIndex, tokenizer.Counts, tokenizer.index = 0, 0, 0

	for {

		tokenizer.index = strings.Index(value[tokenizer.startIndex:], delimiter)

		if tokenizer.index == -1 {
			break
		}

		tokenizer.Tokens[tokenizer.Counts] = value[tokenizer.startIndex : tokenizer.startIndex+tokenizer.index]

		tokenizer.Counts++

		tokenizer.startIndex += tokenizer.index + len(delimiter)
	}

	tokenizer.Tokens[tokenizer.Counts] = value[tokenizer.startIndex:]

	tokenizer.Counts++
}

func Tokenize(word string, tokens []string, startIndices []int, endIndices []int) []string {

	return tokenize(word, tokens, startIndices, endIndices, func(r rune) bool {
		return !unicode.IsLetter(r) && !unicode.IsDigit(r)
	})
}

func tokenize(word string, tokens []string, startIndices []int, endIndices []int, f func(rune) bool) []string {

	count := 0

	start := -1 // valid span start if >= 0

	for end, runes := range word {

		if f(runes) {

			if start >= 0 {

				startIndices[count] = start

				endIndices[count] = end

				count++

				start = ^start
			}
		} else {

			if start < 0 {

				start = end
			}
		}
	}

	// Last field might end at EOF.
	if start >= 0 {

		startIndices[count] = start

		endIndices[count] = len(word)

		count++
	}

	// Create strings from recorded field indices.

	index := 0

	for i := 0; i < count; i++ {

		if endIndices[i]-startIndices[i] > 0 {

			// Check if the token contains at least one letter

			if strings.IndexFunc(word[startIndices[i]:endIndices[i]], unicode.IsLetter) >= 0 {

				tokens[index] = strings.ToLower(word[startIndices[i]:endIndices[i]])

				index++
			}
		}
	}

	return tokens[:index]
}

func ToLower(token string) string {

	bytes := make([]byte, len(token))

	for i := 0; i < len(token); i++ {
		if 'A' <= token[i] && token[i] <= 'Z' {
			bytes[i] = token[i] + ('a' - 'A')
		} else {

			bytes[i] = token[i]
		}
	}
	return BytesToUnsafeString(bytes)
}

// StringToUnsafeBytes unsafe don't use it anywhere
func StringToUnsafeBytes(value string) []byte {
	return unsafe.Slice(unsafe.StringData(value), len(value))
}

// BytesToUnsafeString unsafe don't use it anywhere
func BytesToUnsafeString(bytes []byte) string {
	return unsafe.String(&bytes[0], len(bytes))
}
func Zip(writer *zip.Writer, source string) error {

	err := filepath.Walk(source, func(target string, info os.FileInfo, err error) error {

		if err != nil {

			return err
		}

		header, err := zip.FileInfoHeader(info)

		if err != nil {

			return err
		}

		relPath, err := filepath.Rel(source, target)

		if err != nil {

			return err
		}

		header.Name = relPath

		if info.IsDir() {

			header.Name += "/"

		}

		zipWriter, err := writer.CreateHeader(header)

		if err != nil {

			return err
		}

		if !info.IsDir() {

			var file *os.File

			file, err = os.Open(target)

			if err != nil {

				return err
			}

			defer func(file *os.File) {

				_ = file.Close()
			}(file)

			_, err = io.Copy(zipWriter, file)

			if err != nil {

				return err
			}
		}

		return nil
	})

	if err != nil {

		return err
	}

	return nil
}

func IsIndexStore(dataStoreType DatastoreType) bool {

	if dataStoreType == Index || dataStoreType == MetricIndex || dataStoreType == CorrelatedMetricIndex ||
		dataStoreType == LogIndex || dataStoreType == FlowIndex || dataStoreType == TrapIndex ||
		dataStoreType == NotificationIndex || dataStoreType == AuditIndex || dataStoreType == PolicyIndex || dataStoreType == HealthMetricIndex || dataStoreType == ConfigHistoryIndex ||
		dataStoreType == ComplianceIndex {

		return true
	}

	return false
}

// publish notifications

func Publish(notification MotadataMap) {

	notification[RemoteEventProcessorUUID] = RegistrationId

	bytes, err := json.Marshal(notification)

	if err != nil {

		configLogger.Error(fmt.Sprintf("failed to encode %v, reason: %v", DatastoreOperation, err))
	}

	bufferBytes := &bytes2.Buffer{}

	//2 bytes length
	bufferBytes.WriteByte(byte(len(DatastoreOperation)))

	bufferBytes.WriteByte(byte(len(DatastoreOperation)) >> 8)

	bufferBytes.Write([]byte(DatastoreOperation))

	bufferBytes.Write(snappy.Encode(nil, bytes))

	PublisherResponses <- bufferBytes.Bytes()
}

func CloneDirectory(sourcePath string, destinationPath string) error {

	err := os.Mkdir(destinationPath, 0777)

	if err != nil {

		return err
	}

	err = cp.Copy(sourcePath, destinationPath+PathSeparator)

	if err != nil {

		return err
	}

	return nil
}

func Unzip(zipPath, dst string) error {

	archive, err := zip.OpenReader(zipPath)

	if err != nil {

		return err
	}

	defer archive.Close()

	for _, f := range archive.File {

		filePath := filepath.Join(dst, f.Name)

		if !strings.HasPrefix(filePath, filepath.Clean(dst)+string(os.PathSeparator)) {

			continue
		}

		if f.FileInfo().IsDir() {

			_ = os.MkdirAll(filePath, os.ModePerm)

			continue
		}

		if err = os.MkdirAll(filepath.Dir(filePath), os.ModePerm); err != nil {

			return err
		}

		var file *os.File

		file, err = os.OpenFile(filePath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())

		if err != nil {

			return err
		}

		var archivedFile io.ReadCloser

		archivedFile, err = f.Open()

		if err != nil {

			return err
		}

		if _, err = io.Copy(file, archivedFile); err != nil {

			return err
		}

		_ = file.Close()

		_ = archivedFile.Close()
	}

	return nil
}

/* ----------------------------------- Custom walk dir --------------------------------------*/

func WalkDir(path, sep string, tokenizer *Tokenizer, fn fs.WalkDirFunc) error {

	info, err := os.Lstat(path)

	if err != nil {

		err = fn(path, nil, err)
	} else {

		err = walkDir(path, sep, tokenizer, &statDirEntry{info}, fn)
	}

	if errors.Is(err, fs.SkipDir) || errors.Is(err, fs.SkipAll) {

		return nil
	}

	return err
}

type statDirEntry struct {
	info fs.FileInfo
}

func (entry *statDirEntry) Name() string               { return entry.info.Name() }
func (entry *statDirEntry) IsDir() bool                { return entry.info.IsDir() }
func (entry *statDirEntry) Type() fs.FileMode          { return entry.info.Mode().Type() }
func (entry *statDirEntry) Info() (fs.FileInfo, error) { return entry.info, nil }

func walkDir(path, sep string, tokenizer *Tokenizer, entry fs.DirEntry, walkDirFn fs.WalkDirFunc) error {

	if err := walkDirFn(path, entry, nil); err != nil || !entry.IsDir() {

		if entry.IsDir() && errors.Is(err, fs.SkipDir) {
			// Successfully skipped directory.
			err = nil
		}

		return err
	}

	dirs, err := readDir(path, sep, tokenizer)

	if err != nil {
		// Second call, to report ReadDir error.
		err = walkDirFn(path, entry, err)

		if err != nil {

			if errors.Is(err, fs.SkipDir) && entry.IsDir() {

				err = nil
			}

			return err
		}
	}

	for _, dir := range dirs {

		if err = walkDir(filepath.Join(path, dir.Name()), sep, tokenizer, dir, walkDirFn); err != nil {

			if errors.Is(err, fs.SkipDir) {

				break
			}

			return err
		}
	}

	return nil
}

// readDir reads the directory named by dirname and returns
// a sorted list of directory entries.
func readDir(dirname, sep string, tokenizer *Tokenizer) ([]fs.DirEntry, error) {

	f, err := os.Open(dirname)

	if err != nil {

		return nil, err
	}

	dirs, err := f.ReadDir(-1)

	_ = f.Close()

	if err != nil {

		return nil, err
	}

	sort.Slice(dirs, func(i, j int) bool {

		Split(dirs[i].Name(), sep, tokenizer)

		num1, _ := strconv.Atoi(tokenizer.Tokens[0])

		Split(dirs[j].Name(), sep, tokenizer)

		num2, _ := strconv.Atoi(tokenizer.Tokens[0])

		return num1 < num2
	})

	return dirs, nil
}

func CheckPorts(ip string, ports []string, timeout int) map[string]bool {

	stats := make(map[string]bool)

	for _, port := range ports {

		conn, err := net.DialTimeout("tcp", net.JoinHostPort(ip, port), time.Duration(timeout)*time.Second)

		if err != nil {

			stats[port] = false

		} else {

			if conn != nil {

				stats[port] = true

				_ = conn.Close()

			} else {

				stats[port] = false
			}
		}
	}
	return stats
}

func StringToFloat64(value string) float64 {

	result, _ := strconv.ParseFloat(strings.TrimSpace(value), 64)

	return result
}

func RemapBytes(bufferBytes []byte, size int) []byte {

	var bytes []byte

	var err error

	if bytes, err = MmapAnonymous(len(bufferBytes) + (size * 10)); err != nil {

		bytes = make([]byte, len(bufferBytes)+(size*10))
	}

	copy(bytes, bufferBytes)

	_ = Munmap(bufferBytes)

	return bytes
}

func GetStoreBackupProfile(datastoreType DatastoreType) string {

	switch {

	case datastoreType == Log || datastoreType == LogIndex || datastoreType == LogAggregation:

		return "log"

	case datastoreType == Compliance || datastoreType == ComplianceIndex:

		return "compliance"

	case datastoreType == PerformanceMetric || datastoreType == ObjectStatusMetric || datastoreType == StatusFlapHistory ||
		datastoreType == StaticMetric || datastoreType == MetricAggregation || datastoreType == MetricIndex ||
		datastoreType == CorrelatedMetricIndex || datastoreType == Index || datastoreType == ObjectStatusFlapMetric:

		return "metric"

	case datastoreType == Audit || datastoreType == Notification || datastoreType == AuditIndex || datastoreType == NotificationIndex:

		return "system.event"

	case datastoreType == Trap || datastoreType == TrapIndex || datastoreType == TrapFlapHistory:

		return "trap"

	case datastoreType == ConfigHistory || datastoreType == ConfigHistoryIndex:

		return "config.history"

	case datastoreType == NetRouteMetric || datastoreType == NetRouteStatusMetric || datastoreType == NetRouteStatusMetricAggregation:

		return "netroute"

	case datastoreType == SLOMetric:

		return "slo.metric"
	}

	return Empty
}

func GetFastModN(hash uint64, value int) int {
	return int(uint32(hash)) * value >> 32
}

func RedirectStderr() error {

	os.Stderr.Close()
	// Redirect stderr to a log file
	f, err := os.OpenFile(CurrentDir+PathSeparator+ErrorLogFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)

	if err != nil {

		return err
	}

	os.Stderr = f

	return nil
}
