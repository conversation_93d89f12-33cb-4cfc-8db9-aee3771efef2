package main

import (
	"bytes"
	"fmt"
	"log"
	"math/rand"
	"net"
	"sync"
	"sync/atomic"
	"syscall"
	"time"
)

var payload []byte

var count atomic.Int32

var droppedCount atomic.Int32

var clients = 2

var retrySeconds = 5

var bufferSize = 2

var writeBuffer = make(chan []byte, bufferSize)

var connect atomic.Bool

var connectShutdownNotifications = make(chan bool)

var connUpdates = make(chan net.Conn)

var totalBytes atomic.Int64

func main() {

	data := make([]byte, 1024*1024)

	rand.Read(data)

	buffer := bytes.NewBuffer(nil)

	buffer.Write(data)

	payload = buffer.Bytes()

	var wg = &sync.WaitGroup{}

	go printStats()

	wg.Add(clients)

	for i := 0; i < clients; i++ {

		connect.Store(true)

		go Connect()

		conn := <-connUpdates

		go SendMessages(conn.((*net.TCPConn)))

		go func() {

			defer wg.Done()

			for {

				for range 100000 {

					bufferMessage(payload)
				}

			}

		}()
	}

	wg.Wait()

	connectShutdownNotifications <- true
}

func SendMessages(conn *net.TCPConn) {

	for {

		select {

		case payload = <-writeBuffer:

			n, err := conn.Write(payload)

			if err != nil {

				if err == syscall.EAGAIN || err == syscall.EWOULDBLOCK {

					// Send buffer full, wait using select (poll)
					droppedCount.Add(1)

					fmt.Println("Send buffer full, waiting...")

					continue

				}

				if !connect.Load() {

					connect.Store(true)
				}

				bufferMessage(payload)

				continue
			}

			totalBytes.Add(int64(n))

			count.Add(1)

		case connection := <-connUpdates:

			conn.Close()

			conn = connection.(*net.TCPConn)

		}
	}
}

func bufferMessage(payload []byte) {

	select {

	case writeBuffer <- payload:

	default:

		droppedCount.Add(1)

	}
}

func Connect() {

	ticker := time.NewTicker(time.Duration(retrySeconds) * time.Second)

	for {

		select {

		case <-ticker.C:

			if connect.Load() {

				conn, err := net.Dial("tcp", "localhost:50051")

				if err != nil {

					log.Println("Failed to connect, retrying:", err)

					continue
				}

				rawConn, err := conn.(*net.TCPConn).SyscallConn()

				if err != nil {

					fmt.Println("Failed to get raw connection:", err)

					return
				}

				// Set the socket to non-blocking mode
				err = rawConn.Control(func(fd uintptr) {

					size, _ := syscall.GetsockoptInt(int(fd), syscall.SOL_SOCKET, syscall.SO_RCVBUF)

					fmt.Println(size)

					err = syscall.SetNonblock(int(fd), true)

					if err != nil {

						fmt.Println("Failed to set socket nonblocking:", err)
					}

				})

				connUpdates <- conn

				connect.Store(false)

			}

		case <-connectShutdownNotifications:

			return

		}

	}

}

func printStats() {

	ticker := time.NewTicker(5 * time.Second)

	for range ticker.C {

		fmt.Println(fmt.Sprintf("send count is %v", count.Load()))

		fmt.Println(fmt.Sprintf("dropped count is %v", droppedCount.Load()))

		log.Printf("Throughput: %.2f MBps", float64(totalBytes.Load())/(1024*1024*5))

		count.Store(0)

		droppedCount.Store(0)

		totalBytes.Store(0)
	}
}
