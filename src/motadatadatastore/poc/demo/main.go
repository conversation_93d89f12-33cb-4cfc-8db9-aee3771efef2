package main

import (
	"encoding/json"
	"fmt"
	"motadatadatastore/utils"
	"os"
	"strings"
)

func main() {

	entries, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + "test-dir")

	if err != nil {
		panic(err)
	}

	for _, entry := range entries {

		// Load JSON from a file or define inline (for demo)
		fileContent, err := os.ReadFile(utils.CurrentDir + utils.PathSeparator + "test-dir" + utils.PathSeparator + entry.Name()) // Make sure input.json has your data
		if err != nil {
			panic(err)
		}

		// Parse the JSON
		var data map[string][]interface{}
		if err := json.Unmarshal(fileContent, &data); err != nil {
			panic(err)
		}

		// Map to track which parts exist for each tick
		tickParts := make(map[string]map[string]bool)

		for key := range data {
			parts := strings.Split(key, "^")
			if len(parts) < 3 {
				continue
			}
			tick := parts[0]
			field := parts[2]

			if tickParts[tick] == nil {
				tickParts[tick] = make(map[string]bool)
			}
			tickParts[tick][field] = true
		}

		// Required fields to check
		requiredFields := []string{"max", "min", "last", "object.id.ordinal", "count", "sum"}

		// Identify ticks with missing fields
		var missingTicks []string
		for tick, fields := range tickParts {
			missing := false
			for _, req := range requiredFields {
				if !fields[req] {
					missing = true
					break
				}
			}
			if missing {
				missingTicks = append(missingTicks, tick)
			}
		}

		// Write to file
		outFile := fmt.Sprintf("missing_ticks_%v.txt", entry.Name())
		if err := os.WriteFile(outFile, []byte(strings.Join(missingTicks, "\n")), 0644); err != nil {
			panic(err)
		}

		fmt.Printf("Done. Missing ticks written to %s\n", outFile)

	}

}
