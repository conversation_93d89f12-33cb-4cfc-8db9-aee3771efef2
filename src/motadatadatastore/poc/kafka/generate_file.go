// generate_file.go
package main

//func main() {
//	size := flag.Int("size", 10*1024*1024, "Size of file in bytes (default 10MB)")
//	out := flag.String("out", "data.bin", "Output file name")
//	flag.Parse()
//
//	f, err := os.Create(*out)
//	if err != nil {
//		log.Fatal(err)
//	}
//	defer f.Close()
//
//	buf := make([]byte, 4*1024*1024) // 4MB buffer
//	total := 0
//
//	for total < *size {
//		n := *size - total
//		if n > len(buf) {
//			n = len(buf)
//		}
//		_, err := rand.Read(buf[:n])
//		if err != nil {
//			log.Fatal(err)
//		}
//		_, err = f.Write(buf[:n])
//		if err != nil {
//			log.Fatal(err)
//		}
//		total += n
//	}
//
//	log.Printf("Generated file %s (%d bytes)", *out, *size)
//}
