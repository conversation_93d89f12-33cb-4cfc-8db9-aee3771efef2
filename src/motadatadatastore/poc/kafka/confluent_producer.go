package main

import (
	"flag"
	"fmt"
	"github.com/confluentinc/confluent-kafka-go/kafka"
	"log"
	"os"
	"time"
)

//var totalBytes1 atomic.Int64
//
//var totalMessages atomic.Int64
//
//var msgPerSec int = 250000

func main() {

	broker := flag.String("broker", "localhost:9092", "Kafka broker address")
	topic := flag.String("topic", "my-topic", "Kafka topic name")
	filePath := flag.String("file", "data.bin", "Path to binary data file")
	//minSize := flag.Int("min", 1500, "Minimum chunk size")
	//maxSize := flag.Int("max", 2000, "Maximum chunk size")
	flag.Parse()

	go func() {

		timer := time.NewTicker(time.Second * 10)

		for range timer.C {

			log.Printf("Throughput: %.6f MBps", float64(totalBytes1.Load())/(1024*1024*10))

			log.Printf("total messages: %v", totalMessages.Load()/10)

			totalMessages.Store(0)

			totalBytes1.Store(0)
		}
	}()

	// Load entire file into memory
	data, err := os.ReadFile(*filePath)
	if err != nil {
		log.Fatalf("Failed to read file: %v", err)
	}
	log.Printf("Loaded %d bytes from file", len(data))

	p, err := kafka.NewProducer(&kafka.ConfigMap{"bootstrap.servers": *broker,
		"queue.buffering.max.messages": 100000, // Increase message buffer count
		//"queue.buffering.max.kbytes":   1048576,
	})
	if err != nil {
		panic(err)
	}
	defer p.Close()

	chunkSize := 1500
	//value := "Hello from confluent-kafka-go"

	// Delivery report handler for produced messages
	go func() {
		for range p.Events() {
			//switch ev := e.(type) {
			//case *kafka.Message:
			//	if ev.TopicPartition.Error != nil {
			//		fmt.Printf("Delivery failed: %v\n", ev.TopicPartition)
			//	}
			//}
		}
	}()

	for {

		//start := time.Now()

		for i := 0; i < msgPerSec; i++ {

			err = p.Produce(&kafka.Message{
				TopicPartition: kafka.TopicPartition{Topic: topic, Partition: kafka.PartitionAny},
				Value:          data[:chunkSize],
			}, nil)

			if err != nil {
				fmt.Printf("Produce error: %v\n", err)
			}

			totalBytes1.Add(int64(chunkSize))
			totalMessages.Add(1)
		}

		time.Sleep(time.Second)
	}

	//p.Flush(15 * 1000)
}
