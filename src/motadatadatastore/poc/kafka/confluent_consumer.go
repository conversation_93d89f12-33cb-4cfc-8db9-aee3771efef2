package main

import (
	"fmt"
	"github.com/confluentinc/confluent-kafka-go/kafka"
	"log"
	"sync/atomic"
	"time"
)

//var totalBytes atomic.Int64
//
//var totalmsgs atomic.Int64

func main() {

	go func() {

		timer := time.NewTicker(time.Second * 10)

		for range timer.C {

			log.Printf("Throughput: %.6f MBps, totalmsgs: %v", float64(totalBytes.Load())/(1024*1024*10), totalmsgs.Load()/10)

			totalBytes.Store(0)

			totalmsgs.Store(0)
		}
	}()

	for range 10 {
		go func() {

			c, err := kafka.NewConsumer(&kafka.ConfigMap{
				"bootstrap.servers":       "localhost:9092",
				"group.id":                "myGroup",
				"auto.offset.reset":       "earliest",
				"enable.auto.commit":      false,
				"auto.commit.interval.ms": 1000,
			})
			if err != nil {
				panic(err)
			}
			defer c.Close()

			err = c.SubscribeTopics([]string{"my-topic"}, nil)

			if err != nil {
				panic(err)
			}
			for {

				msg, err := c.ReadMessage(-1)
				if err == nil {
					//fmt.Printf("Message on %s: %s\n", msg.TopicPartition, string(msg.Value))
				} else {
					// Errors like timeout or consumer closed are handled here
					fmt.Printf("Consumer error: %v\n", err)
					break
				}
				totalBytes.Add(int64(len(msg.Value)))
				totalmsgs.Add(1)
			}
		}()
	}

	select {}

}
