package main

import (
	"context"
	"flag"
	"github.com/segmentio/kafka-go"
	"log"
	"os"
	"sync/atomic"
	"time"
)

var totalBytes1 atomic.Int64

var totalMessages atomic.Int64

var msgPerSec int = 50000

func main() {
	broker := flag.String("broker", "localhost:9092", "Kafka broker address")
	topic := flag.String("topic", "my-topic", "Kafka topic name")
	filePath := flag.String("file", "data.bin", "Path to binary data file")
	//minSize := flag.Int("min", 1500, "Minimum chunk size")
	//maxSize := flag.Int("max", 2000, "Maximum chunk size")
	flag.Parse()

	go func() {

		timer := time.NewTicker(time.Second * 10)

		for range timer.C {

			log.Printf("Throughput: %.6f MBps", float64(totalBytes1.Load())/(1024*1024*10))

			log.Printf("total messages: %v", totalMessages.Load()/10)

			totalMessages.Store(0)

			totalBytes1.Store(0)
		}
	}()

	// Load entire file into memory
	data, err := os.ReadFile(*filePath)
	if err != nil {
		log.Fatalf("Failed to read file: %v", err)
	}
	log.Printf("Loaded %d bytes from file", len(data))

	// Kafka writer (async, fire-and-forget)
	writer := kafka.NewWriter(kafka.WriterConfig{
		Brokers: []string{*broker},
		Topic:   *topic,
		Async:   true,
	})
	defer writer.Close()

	//rand.Seed(time.Now().UnixNano())
	totalMessages.Store(0)

	chunkSize := 1500

	for {

		//start := time.Now()

		for i := 0; i < 1; i++ {

			err := writer.WriteMessages(context.Background(), kafka.Message{
				Value: data[:chunkSize],
				//Value: []byte(strconv.Itoa(i)),
			})
			if err != nil {
				log.Printf("Write failed err: %v", err)
			}

			totalBytes1.Add(int64(chunkSize))
			totalMessages.Add(1)

			//time.Sleep(1 * time.Second)

			//totalBytes1.Store(0)
			//totalMessages.Store(0)
			//log.Printf("✅ Sent %d random-sized messages to topic %q", msgCount, *topic)
		}
		//log.Printf("time taken: %v", time.Since(start))
		time.Sleep(50 * time.Second)
	}

}
