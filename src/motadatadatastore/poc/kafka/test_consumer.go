package main

import (
	"context"
	"fmt"
	"github.com/segmentio/kafka-go"
	"log"
)

func main() {

	r1 := kafka.NewReader(kafka.ReaderConfig{
		Brokers: []string{"localhost:9092"},
		Topic:   "my-topic",
		//GroupID:        *groupId,
		QueueCapacity: 100,
		//CommitInterval: time.Second,
		Partition: 0,
		MinBytes:  10e3, // 1KB
		MaxBytes:  10e6, // 10MB
	})

	r2 := kafka.NewReader(kafka.ReaderConfig{
		Brokers: []string{"localhost:9092"},
		Topic:   "my-topic",
		//GroupID:        *groupId,
		QueueCapacity: 100,
		//CommitInterval: time.Second,
		Partition: 0,
		MinBytes:  10e3, // 1KB
		MaxBytes:  10e6,
	})

	go func() {
		for {
			m, err := r1.ReadMessage(context.Background())
			if err != nil {
				log.Printf("error reading message: %v", err)
				continue
			}
			fmt.Printf("1/Partition: %v, Value: %v\n", 0, string(m.Value))
		}
	}()

	go func() {
		for {
			m, err := r2.ReadMessage(context.Background())
			if err != nil {
				log.Printf("error reading message: %v", err)
				continue
			}
			fmt.Printf("2/Partition: %v, Value: %v\n", 1, string(m.Value))
		}
	}()

	select {}
}
