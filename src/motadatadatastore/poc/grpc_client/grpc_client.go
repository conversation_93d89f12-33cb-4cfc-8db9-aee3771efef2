package main

//
//import (
//	"bufio"
//	"context"
//	"fmt"
//	"github.com/shirou/gopsutil/cpu"
//	"github.com/shirou/gopsutil/load"
//	"github.com/shirou/gopsutil/process"
//	"google.golang.org/grpc"
//	"google.golang.org/grpc/credentials/insecure"
//	"google.golang.org/grpc/encoding/gzip"
//	"log"
//	pb "motadatadatastore/poc/grpc_client/pb/pb"
//	"motadatadatastore/utils"
//	"os"
//	"os/signal"
//	"regexp"
//	"strconv"
//	"sync"
//	"sync/atomic"
//	"syscall"
//	"time"
//)
//
//var payload string
//
//var payloadSIzeBytes = 10 * 1024 * 1024
//
//var count atomic.Int64
//
//var clients int = 1
//
//func printStats(file *os.File) {
//	// Get overall CPU usage
//	cpuPercentages, err := cpu.Percent(time.Second, false)
//
//	if err != nil {
//
//		log.Fatalf("Error getting CPU usage: %v", err)
//	}
//
//	file.Write([]byte(fmt.Sprintf("CPU Usage: %.2f%%\n", cpuPercentages[0])))
//
//	// Get per-core CPU usage
//
//	perCoreUsage, err := cpu.Percent(time.Second, true)
//
//	if err != nil {
//
//		log.Fatalf("Error getting per-core CPU usage: %v", err)
//	}
//	for i, usage := range perCoreUsage {
//
//		file.Write([]byte(fmt.Sprintf("Core %d Usage: %.2f%%\n", i, usage)))
//	}
//
//	// Get Load Average
//	loadStats, err := load.Avg()
//
//	if err != nil {
//
//		log.Fatalf("Error getting Load Average: %v", err)
//	}
//	file.Write([]byte(fmt.Sprintf("Load Average: 1 min: %.2f, 5 min: %.2f, 15 min: %.2f\n",
//		loadStats.Load1, loadStats.Load5, loadStats.Load15)))
//
//	// Get current process
//	pid := os.Getpid()
//
//	proc, err := process.NewProcess(int32(pid))
//
//	if err != nil {
//
//		log.Fatalf("Error getting process info: %v", err)
//	}
//
//	// Get Resident Memory Size (RSS)
//	memInfo, err := proc.MemoryInfo()
//
//	if err != nil {
//
//		log.Fatalf("Error getting memory info: %v", err)
//	}
//
//	file.Write([]byte(fmt.Sprintf("Resident Memory Usage (RSS): %d MB\n", memInfo.RSS/1024/1024)))
//}
//
//func avgThroughput(file *os.File) {
//	// Regular expression to extract throughput values
//	re := regexp.MustCompile(`ThroughPut: \s+(\d+)`)
//
//	var totalThroughput, count int
//
//	scanner := bufio.NewScanner(file)
//
//	for scanner.Scan() {
//
//		line := scanner.Text()
//
//		// Extract throughput value using regex
//		matches := re.FindStringSubmatch(line)
//
//		if len(matches) == 2 {
//
//			value, err := strconv.Atoi(matches[1])
//
//			if err == nil {
//
//				totalThroughput += value
//
//				count++
//			}
//		}
//	}
//
//	if err := scanner.Err(); err != nil {
//
//		fmt.Println("Error reading file:", err)
//
//		return
//	}
//
//	// Calculate and print average throughput
//	if count > 0 {
//
//		file.Write([]byte(fmt.Sprintf("Average Throughput: %.2f Mbps\n", float64(totalThroughput)/float64(count))))
//
//	} else {
//
//		file.Write([]byte("No throughput data found\n"))
//	}
//}
//
//func main() {
//
//	// Create a channel to receive OS signals
//	sigChan := make(chan os.Signal, 1)
//
//	// Notify the channel on SIGINT (Ctrl+C) and SIGTERM
//	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
//
//	dir, _ := os.Getwd()
//
//	file, err := os.OpenFile(fmt.Sprintf("%v/send_clients_%v_message_%vKB_%v", dir, clients, payloadSIzeBytes/1024, time.Now().Unix()), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0777)
//
//	if err != nil {
//
//		log.Fatalf("Failed to open file : %v", err)
//	}
//
//	defer file.Close()
//
//	go func() {
//
//		<-sigChan
//
//		printStats(file)
//
//		avgThroughput(file)
//
//		file.Close() // Exit the program
//	}()
//
//	//data := make([]byte, payloadSIzeBytes)
//	//
//	//rand.Read(data)
//	//
//	//buffer := bytes.NewBuffer(nil)
//	//
//	//buffer.Write(data)
//	//
//	//payload = buffer.String()
//
//	payload = utils.GenerateRandomString(payloadSIzeBytes)
//
//	go func() {
//
//		ticker := time.NewTicker(5 * time.Second)
//
//		for range ticker.C {
//
//			file.Write([]byte(fmt.Sprintf("read count is %v\n", count.Load())))
//
//			file.Write([]byte(fmt.Sprintf("ThroughPut: %v\n", count.Load()*int64(payloadSIzeBytes)/(5*1024*1024))))
//
//			count.Store(0)
//		}
//	}()
//
//	var wg sync.WaitGroup
//
//	wg.Add(clients)
//
//	for range clients {
//
//		//grpc.WithDefaultCallOptions(grpc.UseCompressor(gzip.Name))
//		conn, err := grpc.NewClient("localhost:50051", grpc.WithTransportCredentials(insecure.NewCredentials()), grpc.WithDefaultCallOptions(grpc.UseCompressor(gzip.Name), grpc.MaxCallSendMsgSize(50*1024*1024), // Allow sending up to 50MB
//			grpc.MaxCallRecvMsgSize(50*1024*1024)))
//
//		if err != nil {
//
//			log.Fatalf("Failed to connect: %v", err)
//		}
//
//		defer conn.Close()
//
//		client := pb.NewGrpcServiceClient(conn)
//
//		stream, err := client.Listen(context.Background())
//
//		if err != nil {
//
//			log.Fatalf("Error creating stream: %v", err)
//		}
//
//		go func() {
//
//			defer wg.Done()
//
//			for {
//
//				err := stream.Send(&pb.Request{Message: payload})
//
//				if err != nil {
//
//					log.Printf("Failed to send: %v", err)
//
//				}
//
//				count.Add(1)
//			}
//		}()
//	}
//
//	wg.Wait()
//
//}
