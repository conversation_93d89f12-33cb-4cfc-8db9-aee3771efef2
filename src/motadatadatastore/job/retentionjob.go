/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
	We have a backup job that contains all data, but after a long time in the datastore,
	we don't need as many stores to present, so we delete datastores from the timeline that are no longer needed at runtime.
*/

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05             Vedant <PERSON><PERSON><PERSON>tadata-5451  Status Flap New datastore type retention
* 2025-04-02			 Dhaval <PERSON>ra			<PERSON>-4859  Added NetRoute Status Metric Datastore Type
* 2025-05-10             <PERSON><PERSON><PERSON>OTADATA-6174  Added Unmap function to release memory-mapped resources
* 2025-06-24			 <PERSON><PERSON><PERSON>		    MOTADATA-6543  Added Retention for invalid aggregation store and refactored retention for ObjectStatusFlapMetric

* 2025-06-24			 Dhaval Bera			Motadata-6639  Refactoring For Retention Check In Query Parsing
 */

package job

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tidwall/gjson"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"runtime"
	"strings"
	"time"
)

var (
	retentionJobLogger = utils.NewLogger("Retention Job", "job")

	RetentionJobTimer = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day()+1, 03, 00, 00, 00, time.Local).Local()

	DefaultRetentionJobTick = 3

	DefaultHealthMetricRetentionJobTick = 7

	DefaultConfigHistoryJobTick = 30

	DefaultComplianceJobTick = 30

	DefaultSLOMetricRetentionJobTick = 90

	aggregationStoreTypes = map[utils.DatastoreType]struct{}{

		utils.TrapAggregation:   {},
		utils.FlowAggregation:   {},
		utils.LogAggregation:    {},
		utils.MetricAggregation: {},
		utils.PolicyAggregation: {},
	}
)

const (
	retentionFile = "retention.ctx"
)

type RetentionJob struct {
	tokenizer *utils.Tokenizer

	encoder codec.Encoder

	context map[int]int

	ShutdownNotifications chan bool

	shutdown bool
}

func NewRetentionJob() *RetentionJob {

	return &RetentionJob{

		encoder: codec.NewEncoder(utils.NewMemoryPool(5, utils.MaxPoolLength, false, utils.DefaultBlobPools)),

		context: make(map[int]int, 10),

		ShutdownNotifications: make(chan bool, 5),
	}
}

func (job *RetentionJob) Start() {

	_, err := os.Stat(utils.JobDir)

	if os.IsNotExist(err) {

		_ = os.Mkdir(utils.JobDir, 0755)
	}

	job.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	if bytes, err := os.ReadFile(utils.JobDir + utils.PathSeparator + retentionFile); err == nil {

		_ = json.Unmarshal(bytes, &job.context)

	}

	go func() {

		utils.JobEngineShutdownMutex.Add(1)

		for {

			if job.shutdown || utils.GlobalShutdown {

				break
			}

			job.startRetention()

		}

		utils.JobEngineShutdownMutex.Done()

	}()
}

func (job *RetentionJob) startRetention() {

	ticker := time.NewTicker(time.Second * time.Duration(RetentionJobTimer.Sub(time.Now().Local()).Seconds()))

	defer cleanup(ticker)

	for {

		select {

		case event := <-utils.StoreRetentionJobs:

			job.updateStoreRetentionContext(event)

		case <-ticker.C:

			currentTick := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 00, 00, 00, 00, time.Now().Location()).Local()

			if len(job.context) > 0 {

				err := job.deleteStores(currentTick)

				if err != nil {

					retentionJobLogger.Error(fmt.Sprintf("error %v occurred while performing datastore retention", err.Error()))

				}

			}

			err := job.deleteLogs(currentTick)

			if err != nil {

				retentionJobLogger.Error(fmt.Sprintf("error %v occurred while performing log retention", err.Error()))

			}

			ticker.Reset(time.Hour * 24)

		case <-job.ShutdownNotifications:

			retentionJobLogger.Info("shutting down...")

			job.encoder.MemoryPool.Unmap()

			job.shutdown = true

			return

		}

	}

}

func (job *RetentionJob) deleteLogs(currentTick time.Time) error {

	dirs, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory)

	if err != nil {

		return errors.New(fmt.Sprintf("error %v occurred while reading log directory", err.Error()))

	}

	for _, dir := range dirs {

		if !dir.IsDir() {

			continue

		}

		files, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory + utils.PathSeparator + dir.Name())

		if err != nil {

			retentionJobLogger.Error(fmt.Sprintf("error %v occurred while reading %v directory", err.Error(), dir.Name()))

			continue

		}

		for _, file := range files {

			if file.IsDir() {

				continue
			}

			utils.Split(file.Name(), utils.SpaceSeparator, job.tokenizer)

			tick, err := getTimeTick(job.tokenizer.Tokens[0], utils.LogFileDateFormat)

			if err != nil {

				continue
			}

			if int(currentTick.Sub(tick).Hours()) >= utils.LogRetentionJobDays*24 {

				if utils.DebugEnabled() {

					retentionJobLogger.Debug(fmt.Sprintf("log file %v removed in log retention job", file.Name()))

				}

				_ = os.Remove(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory + utils.PathSeparator + dir.Name() + utils.PathSeparator + file.Name())

			}
		}
	}

	return err

}

func (job *RetentionJob) deleteStores(currentTick time.Time) error {

	dirs, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	if err != nil {

		return errors.New(fmt.Sprintf("error %v occurred while reading datstore directory", err.Error()))

	}

	maxRetentionJobTick, retentionJobTick := 0, 0

	for storeType := range job.context {

		if maxRetentionJobTick < job.context[storeType] {

			maxRetentionJobTick = job.context[storeType]
		}
	}

	for _, dir := range dirs {

		if !dir.IsDir() {

			continue

		}

		storeName := dir.Name()

		if strings.Contains(storeName, "corrupted") {

			datastore.DeleteStore(storeName)

			continue
		}

		//skip  mapping stores
		if strings.HasSuffix(storeName, utils.HyphenSeparator+datastore.Mappings) {

			continue
		}

		var storeType utils.DatastoreType

		var store *storage.Store

		if datastore.IsStoreClosed(storeName) {

			// read store type from metadata

			metadata := utils.MotadataMap{}

			bytes, err := os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + storeName + utils.PathSeparator + utils.MetadataFile)

			if err != nil || len(bytes) == 0 {

				datastore.DeleteStore(storeName)

				retentionJobLogger.Error(fmt.Sprintf("failed to open the store %v, reason: metadata file does not exist.", storeName))

				continue
			}

			gjson.ParseBytes(bytes).ForEach(func(key, value gjson.Result) bool {
				metadata[key.String()] = value.Value()
				return true
			})

			storeType = utils.DatastoreType(metadata.GetIntValue(datastore.DatastoreType))

		} else {

			store = datastore.GetStore(storeName, utils.None, false, false, job.encoder, job.tokenizer)

			if store == nil {

				datastore.DeleteStore(storeName)

				continue
			}

			storeType = store.GetDatastoreType()
		}

		if strings.Contains(storeName, utils.AggregationSeparator) {

			utils.Split(storeName, utils.HyphenSeparator, job.tokenizer)

			view := job.tokenizer.Tokens[2] + utils.HyphenSeparator + job.tokenizer.Tokens[3]

			// index of aggregation store
			if job.tokenizer.Counts > 6 {

				view = job.tokenizer.Tokens[3] + utils.HyphenSeparator + job.tokenizer.Tokens[4]

			}

			utils.Split(view, utils.AggregationSeparator, job.tokenizer)

			if !datastore.IsHorizontalAggregationExist(job.tokenizer.Tokens[0], view) {

				if store != nil {

					if !store.IsClosed() {

						store.MarkClosed(job.encoder)
					}
				}

				datastore.DeleteStore(storeName)

				continue
			}

		}

		//MetricIndex searchable posting list need not be deleted, they are global store
		if storeType == utils.StaticMetric || storeType == utils.Mapping || storeType == utils.None {

			continue
		}

		utils.Split(storeName, utils.HyphenSeparator, job.tokenizer)

		tick, err := getTimeTick(job.tokenizer.Tokens[0], utils.StoreDateFormat)

		if err != nil {

			//means global store as we are not able to parse date, searchable stores and -aggregation and all other global stores will be discarded from the retention

			continue
		}

		aggregation := false

		if _, ok := aggregationStoreTypes[storeType]; ok {

			aggregation = true

			//date-storeformat-column-plugin-eventcategory-datatype-interval (total - 7 )
		} else if (storeType == utils.LogIndex || storeType == utils.FlowIndex || storeType == utils.TrapIndex) && job.tokenizer.Counts > 6 {

			aggregation = true
		}

		if storeType == utils.TrapFlapHistory || (storeType == utils.TrapIndex && !aggregation) {

			storeType = utils.Trap

		} else if storeType == utils.PolicyFlapHistory || storeType == utils.EventPolicy || storeType == utils.PolicyResult ||
			storeType == utils.RunbookWorklog || storeType == utils.PolicyAggregation || storeType == utils.PolicyIndex {

			storeType = utils.MetricPolicy

		} else if storeType == utils.StatusFlapHistory || storeType == utils.ObjectStatusMetric || storeType == utils.MetricIndex {

			storeType = utils.PerformanceMetric

		} else if storeType == utils.TrapIndex && aggregation {

			storeType = utils.TrapAggregation

		} else if storeType == utils.NotificationIndex {

			storeType = utils.Notification

		} else if storeType == utils.AuditIndex {

			storeType = utils.Audit

		} else if storeType == utils.LogIndex && aggregation {

			storeType = utils.LogAggregation

		} else if storeType == utils.LogIndex && !aggregation {

			storeType = utils.Log

		} else if storeType == utils.FlowIndex && aggregation {

			storeType = utils.FlowAggregation

		} else if storeType == utils.FlowIndex && !aggregation {

			storeType = utils.Flow

		} else if storeType == utils.NetRouteStatusMetric || storeType == utils.NetRouteStatusMetricAggregation {

			storeType = utils.NetRouteMetric
		}

		// means dummy posting list
		if storeType == utils.Index {

			retentionJobTick = maxRetentionJobTick

		} else if storeType == utils.CorrelationMetric || storeType == utils.CorrelatedMetricIndex {

			retentionJobTick = DefaultRetentionJobTick

		} else if storeType == utils.HealthMetric || storeType == utils.HealthMetricIndex {

			retentionJobTick = DefaultHealthMetricRetentionJobTick

		} else if storeType == utils.ConfigHistory || storeType == utils.ConfigHistoryIndex {

			retentionJobTick = DefaultConfigHistoryJobTick

		} else if storeType == utils.Compliance || storeType == utils.ComplianceIndex {

			retentionJobTick = DefaultComplianceJobTick

		} else if storeType == utils.ObjectStatusFlapMetric {

			retentionJobTick = job.context[int(utils.MetricAggregation)]

			if job.context[int(utils.PerformanceMetric)] > job.context[int(utils.MetricAggregation)] {

				retentionJobTick = job.context[int(utils.PerformanceMetric)]

			}

			retentionJobTick += 7

		} else if storeType == utils.SLOMetric {

			retentionJobTick = DefaultSLOMetricRetentionJobTick

		} else if _, ok := job.context[int(storeType)]; ok {

			retentionJobTick = job.context[int(storeType)]

		} else {

			retentionJobLogger.Error(fmt.Sprintf("invalid context for store %v datastoreType %v", storeName, storeType))

			continue
		}

		if int(currentTick.Sub(tick).Hours()) > retentionJobTick*24 {

			// means rare scenario occurred where store closed in retention but at same time another goroutine opened it,
			//so in that case database behaves randomly or abnormally shutdown hence need to open and close it
			if store == nil {

				store = datastore.GetStore(storeName, utils.None, false, true, job.encoder, job.tokenizer)

				if store == nil {

					datastore.DeleteStore(storeName)

					continue
				}
			}

			if !store.IsClosed() {

				store.MarkClosed(job.encoder)
			}

			if store.IsClosed() {

				datastore.DeleteStore(storeName)
			}

		}

	}

	return err
}

func (job *RetentionJob) updateStoreRetentionContext(event map[int]map[string]int) {

	clear(job.context)

	for storeType, context := range event {

		if _, ok := context[utils.Aggregated]; ok {

			if storeType == int(utils.Log) {

				job.context[int(utils.Log)] = context[utils.Raw]

				job.context[int(utils.LogAggregation)] = context[utils.Aggregated]

			} else if storeType == int(utils.Flow) {

				job.context[int(utils.Flow)] = context[utils.Raw]

				job.context[int(utils.FlowAggregation)] = context[utils.Aggregated]

			} else if storeType == int(utils.Trap) {

				job.context[int(utils.Trap)] = context[utils.Raw]

				job.context[int(utils.TrapAggregation)] = context[utils.Aggregated]

			} else if storeType == int(utils.PerformanceMetric) {

				job.context[int(utils.PerformanceMetric)] = context[utils.Raw]

				job.context[int(utils.MetricAggregation)] = context[utils.Aggregated]

			}

		} else if _, ok = context[utils.Raw]; ok {

			job.context[storeType] = context[utils.Raw]

		}

	}

	bytes, _ := json.MarshalIndent(job.context, "", " ")

	// we persist event context
	err := os.WriteFile(utils.JobDir+utils.PathSeparator+retentionFile, bytes, 0644)

	if err != nil {

		retentionJobLogger.Error(fmt.Sprintf("error %v occurred while updating job context", err.Error()))

	}
}

func getTimeTick(timestamp, format string) (time.Time, error) {

	tick, err := time.ParseInLocation(format, timestamp, time.Local)

	return tick, err
}

func cleanup(ticker *time.Ticker) {

	if err := recover(); err != nil {

		retentionJobLogger.Error(fmt.Sprintf("error %v occurred while processing log retention job", err))

		stackTraceBytes := make([]byte, 1<<20)

		retentionJobLogger.Error(fmt.Sprintf("!!!STACK TRACE for store retention job !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		retentionJobLogger.Error("restarting retention job...")

		ticker.Stop()

		RetentionJobTimer = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day()+1, 02, 00, 00, 00, time.Local).Local()

	}
}

/*

For policy, we are using metric policy as a common datastore type

Datastore type status

    Index - Log raw / Log aggregation based on store name

	PerformanceMetric - metric raw

	StatusMetric - metric raw

	Audit - audit raw

	Notification - notification raw

	PolicyFlapHistory - policy raw

	StatusFlapHistory - metric raw

	TrapFlapHistory - trap raw

	Log - log raw

	Flow - flow raw

	Trap - trap raw

	MetricPolicy - policy raw

	EventPolicy - policy raw

	CorrelationMetric - metric raw

	RunbookWorklog - policy raw

	PolicyResult DatastoreType - policy raw

	None DatastoreType - not needed

	Config DatastoreType - no retention

	MetricAggregation DatastoreType - metric aggregation

	LogAggregation DatastoreType - log aggregation

	FlowAggregation DatastoreType - flow aggregation

	PolicyAggregation DatastoreType - metric policy

	TrapAggregation DatastoreType - trap aggregation

	Mapping DatastoreType - no retention

*/
