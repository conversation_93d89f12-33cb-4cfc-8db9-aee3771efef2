{"citrix.xen.datastore~description": true, "citrix.xen.datastore~free.percent": false, "citrix.xen.datastore~instance.name": true, "citrix.xen.datastore~io.bytes.per.sec": false, "citrix.xen.datastore~io.write.ops.per.sec": true, "citrix.xen.datastore~used.percent": true, "interface": true, "interface~bandwidth": false, "interface~error.packets": false, "interface~in.packets": false, "interface~in.traffic.utilization": false, "interface~instance.name": true, "interface~last.change": false, "interface~out.packets": true, "interface~out.traffic.utilization": false, "interface~received.octets": true, "interface~sent.bytes": false, "interface~sent.octets": true, "system.cpu.cores": false, "system.cpu.percent": false, "system.memory.used.bytes": false, "system.os.name": false, "system.process": true, "system.process~instance.name": true, "system.process~uptime.sec1": false, "system.process~uptime.sec2": false, "system.process~uptime.sec3": false, "system.process~uptime.sec4": false, "system.process~user": true, "vcenter.cpu.utilization.percent": false, "vcenter.memory.installed.bytes": false, "vcenter.node": false, "vcenter.running.virtual.machines": false}