/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
Package job provides store synchronization worker functionality for the Motadata datastore system.

STORE SYNC JOB OVERVIEW

StoreSyncJob is a worker component that performs the actual synchronization of dirty stores
to persistent storage. It operates as part of a worker pool managed by StoreSyncJobManager
and handles the low-level details of store synchronization operations.

SYNCHRONIZATION STRATEGY

The Motadata datastore uses a delayed synchronization approach for optimal performance:

1. WRITE OPERATIONS:
   - Data is written to memory immediately for fast access
   - Keys are persisted to both memory and file structures
   - Write-ahead logs (WAL) are created for transaction safety
   - Actual data synchronization is deferred for performance

2. SYNC OPERATIONS:
   - Performed at regular intervals rather than on every write
   - Ensures data durability without impacting write performance
   - Handles both regular puts and transactional operations
   - Maintains consistency across memory and persistent storage

3. WORKER COORDINATION:
   - Multiple workers operate concurrently for scalability
   - Each worker handles assigned stores independently
   - Acknowledgment system ensures completion tracking
   - Memory pool management prevents resource exhaustion

KEY RESPONSIBILITIES:

1. STORE SYNCHRONIZATION:
   - Receives sync requests from StoreSyncJobManager
   - Performs actual sync operations on assigned stores
   - Handles errors and retries for failed operations
   - Sends acknowledgments upon completion

2. RESOURCE MANAGEMENT:
   - Manages memory pools for encoding operations
   - Performs periodic cleanup to prevent memory bloat
   - Handles graceful shutdown and resource release

3. ERROR HANDLING:
   - Comprehensive error recovery for sync failures
   - Detailed logging for debugging and monitoring
   - Automatic restart capability for worker resilience

PERFORMANCE CONSIDERATIONS:

- Memory pool optimization for encoding operations
- Efficient channel communication for coordination
- Minimal blocking operations for high throughput
- Resource cleanup to prevent memory leaks
*/

/* Change Logs:
* Date                   Author          		Notes
* 2025-04-09			 Dhaval Bera			Motadata-4859  Refactoring "fmt.Sprintf" to update go version to 1.24
* 2025-05-05			 Swapnil A. Dave		MOTADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-05-10             Dhaval Bera            MOTADATA-6174  Added Unmap function to release memory-mapped resources
* 2025-06-23             Dhaval Bera            MOTADATA-6642  Added comments and memory aligned the struct

 */

package job

import (
	"fmt"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/utils"
	"runtime"
	"time"
)

var (
	// syncJobLogger provides structured logging for store sync job operations
	// All sync job operations are logged with "Store Sync Job" prefix and "job" category
	syncJobLogger = utils.NewLogger("Store Sync Job", "job")
)

// StoreSyncEvent represents a synchronization request for a specific store.
// This event is passed between the StoreSyncJobManager and StoreSyncJob workers
// to coordinate store synchronization operations.
//
// The event contains all necessary information for a worker to:
// 1. Identify which store needs synchronization
// 2. Track which worker should handle the operation
// 3. Enable proper acknowledgment routing
type StoreSyncEvent struct {
	// storeName identifies the specific store that needs synchronization
	// Format varies by store type but typically includes plugin and time information
	storeName string

	// syncJobId identifies which worker should handle this sync operation
	// Used for round-robin distribution and acknowledgment routing
	syncJobId int
}

// StoreSyncJob is a worker that performs store synchronization operations as part of a worker pool.
// Each worker operates independently and handles sync requests assigned by the StoreSyncJobManager.
//
// The worker performs the following operations:
// 1. Receives sync requests via notification channels
// 2. Performs actual store synchronization using the datastore API
// 3. Manages memory pools for encoding operations
// 4. Sends acknowledgments upon completion
// 5. Handles errors and provides detailed logging
//
// Memory layout is optimized for 64-bit systems with fields ordered by size to minimize padding.
type StoreSyncJob struct {
	// === 8-byte aligned fields (pointers, channels, interfaces) ===

	// tokenizer provides string parsing functionality for store name processing
	// Used to parse composite store names and extract components during sync operations
	tokenizer *utils.Tokenizer

	// encoder handles data serialization operations during store synchronization
	// Includes dedicated memory pool for this worker to prevent contention
	encoder codec.Encoder

	// syncNotifications receives sync requests from the StoreSyncJobManager
	// Each worker has its own dedicated channel to receive assigned work
	syncNotifications chan StoreSyncEvent

	// storeSyncJobAcknowledgeNotification sends completion acknowledgments back to the manager
	// Shared channel used by all workers to report completion of sync operations
	storeSyncJobAcknowledgeNotification chan StoreSyncEvent

	// ShutdownNotifications receives shutdown signals for graceful termination
	// Buffered channel (capacity: 5) to prevent blocking during shutdown coordination
	ShutdownNotifications chan bool

	// === 4-byte aligned fields (int) ===

	// id uniquely identifies this worker within the worker pool
	// Used for logging, debugging, and worker-specific operations
	id int

	// === 1-byte aligned fields (booleans) ===

	// shutdown indicates whether this worker should stop processing and exit
	// Set to true when a shutdown notification is received
	shutdown bool
}

// NewStoreSyncJob creates and initializes a new StoreSyncJob worker instance.
// This constructor sets up all required components for store synchronization operations
// including dedicated memory pools and communication channels.
//
// The constructor performs the following initialization:
// 1. Assigns unique worker ID for identification and logging
// 2. Creates dedicated encoder with memory pool for this worker
// 3. Sets up communication channels for coordination
// 4. Prepares shutdown notification channel
//
// Parameters:
// - id: Unique identifier for this worker within the pool
// - syncNotification: Channel for receiving sync requests from the manager
// - storeSyncJobAcknowledgeNotification: Shared channel for sending completion acknowledgments
//
// Returns:
// - *StoreSyncJob: Fully initialized worker ready for starting
func NewStoreSyncJob(id int, syncNotification, storeSyncJobAcknowledgeNotification chan StoreSyncEvent) *StoreSyncJob {
	return &StoreSyncJob{
		// Set unique worker identifier
		id: id,

		// Create dedicated encoder with memory pool for this worker
		// Parameters: 5 pools, configurable length, compression enabled, default blob pools
		// Each worker gets its own memory pool to prevent contention
		encoder: codec.NewEncoder(utils.NewMemoryPool(5, utils.SyncJobPoolLength, true, utils.DefaultBlobPools)),

		// Assign dedicated sync notification channel
		syncNotifications: syncNotification,

		// Assign shared acknowledgment channel
		storeSyncJobAcknowledgeNotification: storeSyncJobAcknowledgeNotification,

		// Create buffered shutdown notification channel (capacity: 5)
		ShutdownNotifications: make(chan bool, 5),
	}
}

// Start initializes the StoreSyncJob worker and begins processing sync requests in a background goroutine.
// This method performs final initialization and starts the main processing loop.
//
// The method performs the following operations:
// 1. Initializes the tokenizer for string parsing operations
// 2. Starts the main processing loop in a separate goroutine
// 3. Registers with the global shutdown coordination mechanism
//
// The method returns immediately after starting the background processing.
// The worker will continue running until shutdown is requested.
func (job *StoreSyncJob) Start() {
	// Initialize tokenizer with pre-allocated token buffer
	// This avoids repeated memory allocations during string parsing operations
	job.tokenizer = &utils.Tokenizer{
		Tokens: make([]string, utils.TokenizerLength),
	}

	// Start the main processing loop in a separate goroutine
	go func() {
		// Register this goroutine with the global shutdown coordination mechanism
		// This ensures proper cleanup during system shutdown
		utils.JobEngineShutdownMutex.Add(1)

		// Main processing loop - continues until shutdown is requested
		for {
			// Check for shutdown conditions:
			// - Local shutdown flag set via ShutdownNotifications
			// - Global shutdown flag set by system-wide shutdown process
			if job.shutdown || utils.GlobalShutdown {
				break
			}

			// Execute the main processing logic
			// This method handles all sync requests and timer events
			job.run()
		}

		// Signal completion to the global shutdown coordination mechanism
		// This allows the system to wait for all job engines to complete before exiting
		utils.JobEngineShutdownMutex.Done()
	}()
}

// run is the main execution loop for the StoreSyncJob worker that processes sync requests and timer events.
// This method continuously listens for sync requests and handles memory pool maintenance.
//
// The method handles the following events:
// 1. Memory pool timer: Triggers periodic cleanup to prevent memory bloat
// 2. Sync notifications: Processes store synchronization requests
// 3. Shutdown notifications: Coordinates graceful termination
//
// The method includes comprehensive error recovery to ensure worker stability.
func (job *StoreSyncJob) run() {
	// Set up timer for memory pool maintenance operations
	// Helps prevent memory bloat by shrinking unused pool segments
	poolTimer := time.NewTicker(time.Second * time.Duration(utils.GetMemoryPoolShrinkTimerSeconds()))

	// Set up panic recovery to ensure the worker can recover from unexpected errors
	// This is critical for system stability as sync workers are essential components
	defer func() {
		if err := recover(); err != nil {
			// Stop timer to prevent resource leaks
			poolTimer.Stop()

			// Capture and log the stack trace for debugging
			stackTraceBytes := make([]byte, 1<<20)
			syncJobLogger.Error(fmt.Sprintf("error %v occurred", err))
			syncJobLogger.Error(fmt.Sprintf("!!!STACK TRACE for store sync job !!! \n %v",
				string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
			syncJobLogger.Error(fmt.Sprintf("restarting store sync job - %v", job.id))
		}
	}()

	// Main event processing loop
	for {
		select {
		// Handle memory pool maintenance timer events
		case <-poolTimer.C:
			// Shrink memory pool to release unused segments
			// This prevents memory bloat during low-activity periods
			job.encoder.MemoryPool.ShrinkPool()

		// Handle store synchronization requests
		case event := <-job.syncNotifications:
			// Get store instance for synchronization
			store := datastore.GetStore(event.storeName, utils.None, false, false, job.encoder, job.tokenizer)

			// Only process stores that are open and accessible
			if store != nil && !store.IsClosed() {
				var timestamp int64

				// Log sync operation start for debugging
				if utils.DebugEnabled() {
					syncJobLogger.Debug(fmt.Sprintf("starting sync job %v for store %v", job.id, event.storeName))
					timestamp = time.Now().UnixMilli()
				}

				// Perform the actual store synchronization
				// This operation flushes in-memory data to persistent storage
				if err := store.Sync(job.encoder); err != nil {
					syncJobLogger.Error(fmt.Sprintf(utils.ErrorSyncStore, event.storeName, err.Error()))
				}

				// Log sync operation completion and duration for debugging
				if utils.DebugEnabled() {
					duration := time.Now().UnixMilli() - timestamp
					syncJobLogger.Debug(fmt.Sprintf("sync job %v finished for store %v and took %v ms",
						job.id, event.storeName, duration))
				}
			}

			// Send acknowledgment back to the manager regardless of success/failure
			// This ensures the manager can track completion and assign next work
			job.storeSyncJobAcknowledgeNotification <- event

		// Handle shutdown notification events
		case _ = <-job.ShutdownNotifications:
			// Clean up memory pool resources
			job.encoder.MemoryPool.Unmap()

			syncJobLogger.Info("shutting down store sync job....")

			// Set shutdown flag and exit the processing loop
			job.shutdown = true
			return
		}
	}
}
