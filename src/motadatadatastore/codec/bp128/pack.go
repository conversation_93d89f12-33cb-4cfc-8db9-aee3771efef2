/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05			 <PERSON><PERSON><PERSON>ata-5190  Added sonarignore to allow snake case naming and added nested comment for unimplemented functions
 */

// sonarignore
package bp128

func pack32_0(in uintptr, out *byte, offset int, seed *byte) {

	//this is a reference function
}
func pack32_1(in uintptr, out *byte, offset int, seed *byte)
func pack32_2(in uintptr, out *byte, offset int, seed *byte)
func pack32_3(in uintptr, out *byte, offset int, seed *byte)
func pack32_4(in uintptr, out *byte, offset int, seed *byte)
func pack32_5(in uintptr, out *byte, offset int, seed *byte)
func pack32_6(in uintptr, out *byte, offset int, seed *byte)
func pack32_7(in uintptr, out *byte, offset int, seed *byte)
func pack32_8(in uintptr, out *byte, offset int, seed *byte)
func pack32_9(in uintptr, out *byte, offset int, seed *byte)
func pack32_10(in uintptr, out *byte, offset int, seed *byte)
func pack32_11(in uintptr, out *byte, offset int, seed *byte)
func pack32_12(in uintptr, out *byte, offset int, seed *byte)
func pack32_13(in uintptr, out *byte, offset int, seed *byte)
func pack32_14(in uintptr, out *byte, offset int, seed *byte)
func pack32_15(in uintptr, out *byte, offset int, seed *byte)
func pack32_16(in uintptr, out *byte, offset int, seed *byte)
func pack32_17(in uintptr, out *byte, offset int, seed *byte)
func pack32_18(in uintptr, out *byte, offset int, seed *byte)
func pack32_19(in uintptr, out *byte, offset int, seed *byte)
func pack32_20(in uintptr, out *byte, offset int, seed *byte)
func pack32_21(in uintptr, out *byte, offset int, seed *byte)
func pack32_22(in uintptr, out *byte, offset int, seed *byte)
func pack32_23(in uintptr, out *byte, offset int, seed *byte)
func pack32_24(in uintptr, out *byte, offset int, seed *byte)
func pack32_25(in uintptr, out *byte, offset int, seed *byte)
func pack32_26(in uintptr, out *byte, offset int, seed *byte)
func pack32_27(in uintptr, out *byte, offset int, seed *byte)
func pack32_28(in uintptr, out *byte, offset int, seed *byte)
func pack32_29(in uintptr, out *byte, offset int, seed *byte)
func pack32_30(in uintptr, out *byte, offset int, seed *byte)
func pack32_31(in uintptr, out *byte, offset int, seed *byte)
func pack32_32(in uintptr, out *byte, offset int, seed *byte)

func pack64_0(in uintptr, out *byte, offset int, seed *byte) {

	//this is a reference function
}
func pack64_1(in uintptr, out *byte, offset int, seed *byte)
func pack64_2(in uintptr, out *byte, offset int, seed *byte)
func pack64_3(in uintptr, out *byte, offset int, seed *byte)
func pack64_4(in uintptr, out *byte, offset int, seed *byte)
func pack64_5(in uintptr, out *byte, offset int, seed *byte)
func pack64_6(in uintptr, out *byte, offset int, seed *byte)
func pack64_7(in uintptr, out *byte, offset int, seed *byte)
func pack64_8(in uintptr, out *byte, offset int, seed *byte)
func pack64_9(in uintptr, out *byte, offset int, seed *byte)
func pack64_10(in uintptr, out *byte, offset int, seed *byte)
func pack64_11(in uintptr, out *byte, offset int, seed *byte)
func pack64_12(in uintptr, out *byte, offset int, seed *byte)
func pack64_13(in uintptr, out *byte, offset int, seed *byte)
func pack64_14(in uintptr, out *byte, offset int, seed *byte)
func pack64_15(in uintptr, out *byte, offset int, seed *byte)
func pack64_16(in uintptr, out *byte, offset int, seed *byte)
func pack64_17(in uintptr, out *byte, offset int, seed *byte)
func pack64_18(in uintptr, out *byte, offset int, seed *byte)
func pack64_19(in uintptr, out *byte, offset int, seed *byte)
func pack64_20(in uintptr, out *byte, offset int, seed *byte)
func pack64_21(in uintptr, out *byte, offset int, seed *byte)
func pack64_22(in uintptr, out *byte, offset int, seed *byte)
func pack64_23(in uintptr, out *byte, offset int, seed *byte)
func pack64_24(in uintptr, out *byte, offset int, seed *byte)
func pack64_25(in uintptr, out *byte, offset int, seed *byte)
func pack64_26(in uintptr, out *byte, offset int, seed *byte)
func pack64_27(in uintptr, out *byte, offset int, seed *byte)
func pack64_28(in uintptr, out *byte, offset int, seed *byte)
func pack64_29(in uintptr, out *byte, offset int, seed *byte)
func pack64_30(in uintptr, out *byte, offset int, seed *byte)
func pack64_31(in uintptr, out *byte, offset int, seed *byte)
func pack64_32(in uintptr, out *byte, offset int, seed *byte)
func pack64_33(in uintptr, out *byte, offset int, seed *byte)
func pack64_34(in uintptr, out *byte, offset int, seed *byte)
func pack64_35(in uintptr, out *byte, offset int, seed *byte)
func pack64_36(in uintptr, out *byte, offset int, seed *byte)
func pack64_37(in uintptr, out *byte, offset int, seed *byte)
func pack64_38(in uintptr, out *byte, offset int, seed *byte)
func pack64_39(in uintptr, out *byte, offset int, seed *byte)
func pack64_40(in uintptr, out *byte, offset int, seed *byte)
func pack64_41(in uintptr, out *byte, offset int, seed *byte)
func pack64_42(in uintptr, out *byte, offset int, seed *byte)
func pack64_43(in uintptr, out *byte, offset int, seed *byte)
func pack64_44(in uintptr, out *byte, offset int, seed *byte)
func pack64_45(in uintptr, out *byte, offset int, seed *byte)
func pack64_46(in uintptr, out *byte, offset int, seed *byte)
func pack64_47(in uintptr, out *byte, offset int, seed *byte)
func pack64_48(in uintptr, out *byte, offset int, seed *byte)
func pack64_49(in uintptr, out *byte, offset int, seed *byte)
func pack64_50(in uintptr, out *byte, offset int, seed *byte)
func pack64_51(in uintptr, out *byte, offset int, seed *byte)
func pack64_52(in uintptr, out *byte, offset int, seed *byte)
func pack64_53(in uintptr, out *byte, offset int, seed *byte)
func pack64_54(in uintptr, out *byte, offset int, seed *byte)
func pack64_55(in uintptr, out *byte, offset int, seed *byte)
func pack64_56(in uintptr, out *byte, offset int, seed *byte)
func pack64_57(in uintptr, out *byte, offset int, seed *byte)
func pack64_58(in uintptr, out *byte, offset int, seed *byte)
func pack64_59(in uintptr, out *byte, offset int, seed *byte)
func pack64_60(in uintptr, out *byte, offset int, seed *byte)
func pack64_61(in uintptr, out *byte, offset int, seed *byte)
func pack64_62(in uintptr, out *byte, offset int, seed *byte)
func pack64_63(in uintptr, out *byte, offset int, seed *byte)
func pack64_64(in uintptr, out *byte, offset int, seed *byte)

func dpack32_0(in uintptr, out *byte, offset int, seed *byte) {

	//this is a reference function
}
func dpack32_1(in uintptr, out *byte, offset int, seed *byte)
func dpack32_2(in uintptr, out *byte, offset int, seed *byte)
func dpack32_3(in uintptr, out *byte, offset int, seed *byte)
func dpack32_4(in uintptr, out *byte, offset int, seed *byte)
func dpack32_5(in uintptr, out *byte, offset int, seed *byte)
func dpack32_6(in uintptr, out *byte, offset int, seed *byte)
func dpack32_7(in uintptr, out *byte, offset int, seed *byte)
func dpack32_8(in uintptr, out *byte, offset int, seed *byte)
func dpack32_9(in uintptr, out *byte, offset int, seed *byte)
func dpack32_10(in uintptr, out *byte, offset int, seed *byte)
func dpack32_11(in uintptr, out *byte, offset int, seed *byte)
func dpack32_12(in uintptr, out *byte, offset int, seed *byte)
func dpack32_13(in uintptr, out *byte, offset int, seed *byte)
func dpack32_14(in uintptr, out *byte, offset int, seed *byte)
func dpack32_15(in uintptr, out *byte, offset int, seed *byte)
func dpack32_16(in uintptr, out *byte, offset int, seed *byte)
func dpack32_17(in uintptr, out *byte, offset int, seed *byte)
func dpack32_18(in uintptr, out *byte, offset int, seed *byte)
func dpack32_19(in uintptr, out *byte, offset int, seed *byte)
func dpack32_20(in uintptr, out *byte, offset int, seed *byte)
func dpack32_21(in uintptr, out *byte, offset int, seed *byte)
func dpack32_22(in uintptr, out *byte, offset int, seed *byte)
func dpack32_23(in uintptr, out *byte, offset int, seed *byte)
func dpack32_24(in uintptr, out *byte, offset int, seed *byte)
func dpack32_25(in uintptr, out *byte, offset int, seed *byte)
func dpack32_26(in uintptr, out *byte, offset int, seed *byte)
func dpack32_27(in uintptr, out *byte, offset int, seed *byte)
func dpack32_28(in uintptr, out *byte, offset int, seed *byte)
func dpack32_29(in uintptr, out *byte, offset int, seed *byte)
func dpack32_30(in uintptr, out *byte, offset int, seed *byte)
func dpack32_31(in uintptr, out *byte, offset int, seed *byte)
func dpack32_32(in uintptr, out *byte, offset int, seed *byte)

func dpack64_0(in uintptr, out *byte, offset int, seed *byte) {

	//this is a reference function
}
func dpack64_1(in uintptr, out *byte, offset int, seed *byte)
func dpack64_2(in uintptr, out *byte, offset int, seed *byte)
func dpack64_3(in uintptr, out *byte, offset int, seed *byte)
func dpack64_4(in uintptr, out *byte, offset int, seed *byte)
func dpack64_5(in uintptr, out *byte, offset int, seed *byte)
func dpack64_6(in uintptr, out *byte, offset int, seed *byte)
func dpack64_7(in uintptr, out *byte, offset int, seed *byte)
func dpack64_8(in uintptr, out *byte, offset int, seed *byte)
func dpack64_9(in uintptr, out *byte, offset int, seed *byte)
func dpack64_10(in uintptr, out *byte, offset int, seed *byte)
func dpack64_11(in uintptr, out *byte, offset int, seed *byte)
func dpack64_12(in uintptr, out *byte, offset int, seed *byte)
func dpack64_13(in uintptr, out *byte, offset int, seed *byte)
func dpack64_14(in uintptr, out *byte, offset int, seed *byte)
func dpack64_15(in uintptr, out *byte, offset int, seed *byte)
func dpack64_16(in uintptr, out *byte, offset int, seed *byte)
func dpack64_17(in uintptr, out *byte, offset int, seed *byte)
func dpack64_18(in uintptr, out *byte, offset int, seed *byte)
func dpack64_19(in uintptr, out *byte, offset int, seed *byte)
func dpack64_20(in uintptr, out *byte, offset int, seed *byte)
func dpack64_21(in uintptr, out *byte, offset int, seed *byte)
func dpack64_22(in uintptr, out *byte, offset int, seed *byte)
func dpack64_23(in uintptr, out *byte, offset int, seed *byte)
func dpack64_24(in uintptr, out *byte, offset int, seed *byte)
func dpack64_25(in uintptr, out *byte, offset int, seed *byte)
func dpack64_26(in uintptr, out *byte, offset int, seed *byte)
func dpack64_27(in uintptr, out *byte, offset int, seed *byte)
func dpack64_28(in uintptr, out *byte, offset int, seed *byte)
func dpack64_29(in uintptr, out *byte, offset int, seed *byte)
func dpack64_30(in uintptr, out *byte, offset int, seed *byte)
func dpack64_31(in uintptr, out *byte, offset int, seed *byte)
func dpack64_32(in uintptr, out *byte, offset int, seed *byte)
func dpack64_33(in uintptr, out *byte, offset int, seed *byte)
func dpack64_34(in uintptr, out *byte, offset int, seed *byte)
func dpack64_35(in uintptr, out *byte, offset int, seed *byte)
func dpack64_36(in uintptr, out *byte, offset int, seed *byte)
func dpack64_37(in uintptr, out *byte, offset int, seed *byte)
func dpack64_38(in uintptr, out *byte, offset int, seed *byte)
func dpack64_39(in uintptr, out *byte, offset int, seed *byte)
func dpack64_40(in uintptr, out *byte, offset int, seed *byte)
func dpack64_41(in uintptr, out *byte, offset int, seed *byte)
func dpack64_42(in uintptr, out *byte, offset int, seed *byte)
func dpack64_43(in uintptr, out *byte, offset int, seed *byte)
func dpack64_44(in uintptr, out *byte, offset int, seed *byte)
func dpack64_45(in uintptr, out *byte, offset int, seed *byte)
func dpack64_46(in uintptr, out *byte, offset int, seed *byte)
func dpack64_47(in uintptr, out *byte, offset int, seed *byte)
func dpack64_48(in uintptr, out *byte, offset int, seed *byte)
func dpack64_49(in uintptr, out *byte, offset int, seed *byte)
func dpack64_50(in uintptr, out *byte, offset int, seed *byte)
func dpack64_51(in uintptr, out *byte, offset int, seed *byte)
func dpack64_52(in uintptr, out *byte, offset int, seed *byte)
func dpack64_53(in uintptr, out *byte, offset int, seed *byte)
func dpack64_54(in uintptr, out *byte, offset int, seed *byte)
func dpack64_55(in uintptr, out *byte, offset int, seed *byte)
func dpack64_56(in uintptr, out *byte, offset int, seed *byte)
func dpack64_57(in uintptr, out *byte, offset int, seed *byte)
func dpack64_58(in uintptr, out *byte, offset int, seed *byte)
func dpack64_59(in uintptr, out *byte, offset int, seed *byte)
func dpack64_60(in uintptr, out *byte, offset int, seed *byte)
func dpack64_61(in uintptr, out *byte, offset int, seed *byte)
func dpack64_62(in uintptr, out *byte, offset int, seed *byte)
func dpack64_63(in uintptr, out *byte, offset int, seed *byte)
func dpack64_64(in uintptr, out *byte, offset int, seed *byte)
