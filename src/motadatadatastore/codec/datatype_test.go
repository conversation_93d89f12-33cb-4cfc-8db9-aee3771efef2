/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package codec

import (
	"github.com/stretchr/testify/assert"
	"math"
	"motadatadatastore/utils"
	"testing"
)

func TestGetDataTypeINT(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(GetDataTypeINT(math.MaxInt8), Int8)

	assertions.Equal(GetDataTypeINT(math.MaxInt16), Int16)

	assertions.Equal(GetDataTypeINT(MaxInt24), Int24)

	assertions.Equal(GetDataTypeINT(math.MaxInt32), Int32)

	assertions.Equal(GetDataTypeINT(MaxInt40), Int40)

	assertions.Equal(GetDataTypeINT(MaxInt48), Int48)

	assertions.Equal(GetDataTypeINT(MaxInt56), Int56)

	assertions.Equal(GetDataTypeINT(math.MaxInt64), Int64)

	assertions.Equal(GetDataTypeBytes(Int24), 3)

	assertions.Equal(GetDataTypeBytes(Int40), 5)

	assertions.Equal(GetDataTypeBytes(Int48), 6)

	assertions.Equal(GetDataTypeBytes(Int56), 7)

	assertions.Equal(GetDataTypeBytes(Float8), 2)

	assertions.Equal(GetDataTypeBytes(Float16), 3)

	assertions.Equal(GetDataTypeBytes(Float64), 8)

	assertions.Equal(GetDataTypeBits(Int8), uint8(8))

}

func TestGetDataTypeFloat(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(GetDataTypeFloat(math.MaxFloat64), Float64)

	assertions.Equal(GetDataTypeFloat(math.MaxInt8+0.01), Float8)

	assertions.Equal(GetDataTypeFloat(math.MaxInt16+0.01), Float16)

	assertions.Equal(INT64ToFLOAT64Values([]int64{1, 2, 3, 4}, make([]float64, 4)), []float64{1, 2, 3, 4})

	assertions.Equal(INT8ToFLOAT64Values([]int8{1, 2, 3, 4}, make([]float64, 4)), []float64{1, 2, 3, 4})

	assertions.Equal(INT16ToFLOAT64Values([]int16{1, 2, 3, 4}, make([]float64, 4)), []float64{1, 2, 3, 4})

	assertions.Equal(INT32ToFLOAT64Values([]int32{1, 2, 3, 4}, make([]float64, 4)), []float64{1, 2, 3, 4})

	assertions.Equal(INT64ToFLOAT64DummyValues([]int64{1, utils.DummyINT64Value, 2, 3}, make([]float64, 4)), []float64{1, 0, 2, 3})
}

func TestToINT(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(ToINT("1"), 1)

	assertions.Equal(ToINT(uint(1)), 1)

	assertions.Equal(ToINT(uint8(1)), 1)

	assertions.Equal(ToINT(uint16(1)), 1)

	assertions.Equal(ToINT(uint32(1)), 1)

	assertions.Equal(ToINT(uint64(1)), 1)

	assertions.Equal(ToINT(1), 1)

	assertions.Equal(ToINT(int8(1)), 1)

	assertions.Equal(ToINT(int16(1)), 1)

	assertions.Equal(ToINT(int32(1)), 1)

	assertions.Equal(ToINT(int64(1)), 1)

	assertions.Equal(ToINT(1.023), 1)

	assertions.Equal(ToINT(float32(1.023)), 1)

}

func TestToINT64(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(ToINT64("1"), int64(1))

	assertions.Equal(ToINT64(uint(1)), int64(1))

	assertions.Equal(ToINT64(uint8(1)), int64(1))

	assertions.Equal(ToINT64(uint16(1)), int64(1))

	assertions.Equal(ToINT64(uint32(1)), int64(1))

	assertions.Equal(ToINT64(uint64(1)), int64(1))

	assertions.Equal(ToINT64(1), int64(1))

	assertions.Equal(ToINT64(int8(1)), int64(1))

	assertions.Equal(ToINT64(int16(1)), int64(1))

	assertions.Equal(ToINT64(int32(1)), int64(1))

	assertions.Equal(ToINT64(int64(1)), int64(1))

	assertions.Equal(ToINT64(1.023), int64(1))

	assertions.Equal(ToINT64(float32(1.023)), int64(1))

}

func TestContains(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(utils.Contains([]interface{}{"hello", "hello1"}, "hello"), true)

	assertions.Equal(utils.Contains([]interface{}{"hello", "hello1"}, "hello3"), false)

}

func TestStringDataTypeConversion(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(StringToINT("1"), 1)

	assertions.Equal(StringToFloat64("1.00"), 1.00)

	assertions.Equal(StringToINT32("1"), int32(1))

	assertions.Equal(StringToINT64("1"), int64(1))

	assertions.Equal(INT8ToStringValue(int8(1)), "1")

	assertions.Equal(INT16ToStringValue(int16(1)), "1")

	assertions.Equal(INT32ToStringValue(int32(1)), "1")

	assertions.Equal(INT64ToStringValue(int64(1)), "1")

	assertions.Equal(FLOAT64ToStringValue(1.087), "1")

	assertions.Equal(INTToStringValue(1), "1")

	assertions.Equal(ToString("hello"), "hello")

	assertions.Equal(ToString("hello"), "hello")

	assertions.Equal(ToString(int8(24)), "24")

	assertions.Equal(ToString(int32(24)), "24")

	assertions.Equal(ToString(int64(24)), "24")

	assertions.Equal(ToString(24), "24")

	assertions.Equal(ToString(uint32(24)), "24")

	assertions.Equal(ToString(uint64(24)), "24")

	assertions.Equal(ToString(float32(24.00)), "24")

	assertions.Equal(ToString(24.08), "24")

	assertions.Equal(ToString(true), "true")

	assertions.Equal(GetDataTypeString("1"), Int8)

	assertions.Equal(GetDataTypeString("hello"), String)

	assertions.Equal(FLOAT64ToStringDummyValues([]float64{1.1, 2.2}, []string{"hello", "hello"}), []string{"1", "2"})

	assertions.Equal(FLOAT64ToStringValues([]float64{1.1, 2.2}, []string{"hello", "hello"}), []string{"1", "2"})

	assertions.Equal(INT64ToStringValues([]int64{1, 2}, []string{"hello", "hello"}), []string{"1", "2"})

	assertions.Equal(INT64ToStringDummyValues([]int64{1, 2}, []string{"hello", "hello"}), []string{"1", "2"})

}

func TestINTDataTypeConversion(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(INT8ToINT16Values([]int8{1, 1}, []int16{1, 2}), []int16{1, 1})

	assertions.Equal(INT8ToINT32Values([]int8{1, 1}, []int32{1, 2}), []int32{1, 1})

	assertions.Equal(INT8ToINT64Values([]int8{1, 1}, []int64{1, 2}), []int64{1, 1})

	assertions.Equal(INT16ToINT32Values([]int16{1, 1}, []int32{1, 2}), []int32{1, 1})

	assertions.Equal(INT16ToINT64Values([]int16{1, 1}, []int64{1, 2}), []int64{1, 1})

	assertions.Equal(INT32ToINT64Values([]int32{1, 1}, []int64{1, 2}), []int64{1, 1})

	assertions.EqualValues(INTToFLOATValues([]interface{}{1, 2, 3}), []interface{}{float64(1), float64(2), float64(3)})

	assertions.Equal(INT64ToFLOAT64Value(123), float64(123))

	assertions.Equal(GetMaxDataTypeINT64Values([]int64{111, 222, 33, 444, 5678}), Int16)

	assertions.Equal(GetMaxDataTypeFLOAT64Values([]float64{111.09, 222.567, 33.765, 444.8765, 56787.876}), Float64)

	assertions.Equal(ToINTValues([]interface{}{true, false}, []int{1, 1}), []int{1, 0})

	assertions.Equal(ToINTValues([]interface{}{}, []int{1, 1}), []int{1, 1})

	value := Float(-123)

	assertions.Equal(value.DecimalPart(), -int8(value-value/FractionPart*FractionPart))

}
