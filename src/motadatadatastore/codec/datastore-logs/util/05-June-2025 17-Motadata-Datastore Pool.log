05-June-2025 05:32:50.514411 PM FATAL [Pool]:BYTE pool 3 not released, reason: BYTE pool leaked
05-June-2025 05:32:50.514549 PM ERROR [Pool]:goroutine 136 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseBytePool(0xc002bb3b30?, 0x92f7e0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:265 +0x33d
motadatadatastore/codec.TestZstdCodecStringVersionPoolLength5000(0xc0005d6e00)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:4877 +0xdee
testing.tRunner(0xc0005d6e00, 0xa32758)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:50.515147 PM FATAL [Pool]:STRING pool 0 not released, reason: STRING pool leaked
05-June-2025 05:32:50.515171 PM ERROR [Pool]:goroutine 136 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseStringPool(0xc002bb3b30?, 0x92f7e0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:709 +0x236
motadatadatastore/codec.TestZstdCodecStringVersionPoolLength5000(0xc0005d6e00)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:4879 +0xe05
testing.tRunner(0xc0005d6e00, 0xa32758)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:50.836151 PM FATAL [Pool]:BYTE pool 3 not released, reason: BYTE pool leaked
05-June-2025 05:32:50.836244 PM ERROR [Pool]:goroutine 137 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseBytePool(0xc00abe9b30?, 0x92f7e0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:265 +0x33d
motadatadatastore/codec.TestZstdCodecStringVersionPoolLength10000(0xc0005d6fc0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:4918 +0xdee
testing.tRunner(0xc0005d6fc0, 0xa32720)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:50.836319 PM FATAL [Pool]:STRING pool 0 not released, reason: STRING pool leaked
05-June-2025 05:32:50.836372 PM ERROR [Pool]:goroutine 137 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseStringPool(0xc00abe9b30?, 0x92f7e0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:709 +0x236
motadatadatastore/codec.TestZstdCodecStringVersionPoolLength10000(0xc0005d6fc0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:4920 +0xe05
testing.tRunner(0xc0005d6fc0, 0xa32720)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:51.234316 PM FATAL [Pool]:BYTE pool 3 not released, reason: BYTE pool leaked
05-June-2025 05:32:51.234383 PM ERROR [Pool]:goroutine 161 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseBytePool(0xc003023b30?, 0x92f7e0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:265 +0x33d
motadatadatastore/codec.TestZstdCodecStringVersionPoolLength15000(0xc0005d6e00)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:4959 +0xdee
testing.tRunner(0xc0005d6e00, 0xa32728)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:51.234440 PM FATAL [Pool]:STRING pool 0 not released, reason: STRING pool leaked
05-June-2025 05:32:51.234473 PM ERROR [Pool]:goroutine 161 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseStringPool(0xc003023b30?, 0x92f7e0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:709 +0x236
motadatadatastore/codec.TestZstdCodecStringVersionPoolLength15000(0xc0005d6e00)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:4961 +0xe05
testing.tRunner(0xc0005d6e00, 0xa32728)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:51.694277 PM FATAL [Pool]:BYTE pool 3 not released, reason: BYTE pool leaked
05-June-2025 05:32:51.694352 PM ERROR [Pool]:goroutine 178 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseBytePool(0xc005b53b30?, 0x92f7e0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:265 +0x33d
motadatadatastore/codec.TestZstdCodecStringVersionPoolLength20000(0xc0005d6fc0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:5000 +0xdee
testing.tRunner(0xc0005d6fc0, 0xa32730)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:51.694415 PM FATAL [Pool]:STRING pool 0 not released, reason: STRING pool leaked
05-June-2025 05:32:51.694443 PM ERROR [Pool]:goroutine 178 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseStringPool(0xc005b53b30?, 0x92f7e0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:709 +0x236
motadatadatastore/codec.TestZstdCodecStringVersionPoolLength20000(0xc0005d6fc0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:5002 +0xe05
testing.tRunner(0xc0005d6fc0, 0xa32730)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:52.238287 PM FATAL [Pool]:BYTE pool 3 not released, reason: BYTE pool leaked
05-June-2025 05:32:52.238363 PM ERROR [Pool]:goroutine 179 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseBytePool(0xc004a2bb30?, 0x92f7e0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:265 +0x33d
motadatadatastore/codec.TestZstdCodecStringVersionPoolLength25000(0xc0005d6e00)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:5041 +0xdee
testing.tRunner(0xc0005d6e00, 0xa32738)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:52.238424 PM FATAL [Pool]:STRING pool 0 not released, reason: STRING pool leaked
05-June-2025 05:32:52.238447 PM ERROR [Pool]:goroutine 179 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseStringPool(0xc004a2bb30?, 0x92f7e0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:709 +0x236
motadatadatastore/codec.TestZstdCodecStringVersionPoolLength25000(0xc0005d6e00)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:5043 +0xe05
testing.tRunner(0xc0005d6e00, 0xa32738)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:52.842279 PM FATAL [Pool]:BYTE pool 3 not released, reason: BYTE pool leaked
05-June-2025 05:32:52.842354 PM ERROR [Pool]:goroutine 138 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseBytePool(0xc004a2bb30?, 0x92f7e0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:265 +0x33d
motadatadatastore/codec.TestZstdCodecStringVersionPoolLength30000(0xc0005d6fc0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:5082 +0xdee
testing.tRunner(0xc0005d6fc0, 0xa32740)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:52.842413 PM FATAL [Pool]:STRING pool 0 not released, reason: STRING pool leaked
05-June-2025 05:32:52.842437 PM ERROR [Pool]:goroutine 138 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseStringPool(0xc004a2bb30?, 0x92f7e0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:709 +0x236
motadatadatastore/codec.TestZstdCodecStringVersionPoolLength30000(0xc0005d6fc0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:5084 +0xe05
testing.tRunner(0xc0005d6fc0, 0xa32740)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:53.513133 PM FATAL [Pool]:BYTE pool 3 not released, reason: BYTE pool leaked
05-June-2025 05:32:53.513203 PM ERROR [Pool]:goroutine 139 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseBytePool(0xc003ec9b30?, 0x92f7e0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:265 +0x33d
motadatadatastore/codec.TestZstdCodecStringVersionPoolLength35000(0xc0005d6e00)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:5123 +0xdee
testing.tRunner(0xc0005d6e00, 0xa32748)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:53.513260 PM FATAL [Pool]:STRING pool 0 not released, reason: STRING pool leaked
05-June-2025 05:32:53.513284 PM ERROR [Pool]:goroutine 139 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseStringPool(0xc003ec9b30?, 0x92f7e0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:709 +0x236
motadatadatastore/codec.TestZstdCodecStringVersionPoolLength35000(0xc0005d6e00)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:5125 +0xe05
testing.tRunner(0xc0005d6e00, 0xa32748)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:54.278272 PM FATAL [Pool]:BYTE pool 3 not released, reason: BYTE pool leaked
05-June-2025 05:32:54.278361 PM ERROR [Pool]:goroutine 180 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseBytePool(0xc004c5bb30?, 0x92f7e0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:265 +0x33d
motadatadatastore/codec.TestZstdCodecStringVersionPoolLength40000(0xc0005d6fc0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:5164 +0xdee
testing.tRunner(0xc0005d6fc0, 0xa32750)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:54.278691 PM FATAL [Pool]:STRING pool 0 not released, reason: STRING pool leaked
05-June-2025 05:32:54.278733 PM ERROR [Pool]:goroutine 180 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseStringPool(0xc004c5bb30?, 0x92f7e0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:709 +0x236
motadatadatastore/codec.TestZstdCodecStringVersionPoolLength40000(0xc0005d6fc0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:5166 +0xe05
testing.tRunner(0xc0005d6fc0, 0xa32750)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:54.297903 PM FATAL [Pool]:STRING pool 5 not released, reason: STRING pool leaked
05-June-2025 05:32:54.297975 PM ERROR [Pool]:goroutine 194 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseStringPool(0xc005f23cb0?, 0x929c20?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:709 +0x236
motadatadatastore/codec.TestSnappyCodecStringVersion2(0xc0005d6e00?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:5198 +0xb7d
testing.tRunner(0xc0005d6e00, 0xa32608)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:54.317406 PM FATAL [Pool]:STRING pool 5 not released, reason: STRING pool leaked
05-June-2025 05:32:54.317470 PM ERROR [Pool]:goroutine 181 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseStringPool(0xc005f33b30?, 0x929c20?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:709 +0x236
motadatadatastore/codec.TestSnappyCodecStringVersion3(0xc0005d7340?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:5234 +0xbe9
testing.tRunner(0xc0005d7340, 0xa32610)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:56.544524 PM FATAL [Pool]:UINT32 pool -1 not released, reason: UINT32 pool leaked
05-June-2025 05:32:56.544598 PM ERROR [Pool]:goroutine 213 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseUINT32Pool(0xd1fc80?, 0x92fa20?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:882 +0x236
motadatadatastore/codec.Decoder.decodeZigZagDeltaINT32Values({0xc0000de300?, 0xc0001a9810?, 0xc0000c0fa0?}, {0xc00242a000?, 0x3?, 0x3?}, 0x0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/decoder.go:878 +0x20d
motadatadatastore/codec.TestInvalidDecodeZigZagDeltaINT32(0xc0005d7dc0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:6226 +0x1c5
testing.tRunner(0xc0005d7dc0, 0xa32450)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:56.545224 PM FATAL [Pool]:INT32 pool -1 not released, reason: INT32 pool leaked
05-June-2025 05:32:56.545285 PM ERROR [Pool]:goroutine 213 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseINT32Pool(0xe83b793?, 0xc0005d7dc0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:623 +0x236
motadatadatastore/codec.TestInvalidDecodeZigZagDeltaINT32(0xc0005d7dc0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:6236 +0x29c
testing.tRunner(0xc0005d7dc0, 0xa32450)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:56.546064 PM FATAL [Pool]:INT32 pool -1 not released, reason: INT32 pool leaked
05-June-2025 05:32:56.546150 PM ERROR [Pool]:goroutine 214 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseINT32Pool(0xe83b793?, 0xc000092a80?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:623 +0x236
motadatadatastore/codec.TestInvalidDecodeDeltaINT32(0xc000092a80?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:6264 +0x29c
testing.tRunner(0xc000092a80, 0xa323c8)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:56.547020 PM FATAL [Pool]:INT32 pool -1 not released, reason: INT32 pool leaked
05-June-2025 05:32:56.547115 PM ERROR [Pool]:goroutine 215 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseINT32Pool(0xe83b793?, 0xc000093c00?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:623 +0x236
motadatadatastore/codec.TestInvalidParquetDeltaINT32(0xc000093c00?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:6328 +0x718
testing.tRunner(0xc000093c00, 0xa32460)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:56.548063 PM FATAL [Pool]:UINT64 pool -1 not released, reason: UINT64 pool leaked
05-June-2025 05:32:56.548138 PM ERROR [Pool]:goroutine 217 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseUINT64Pool(0xd1fc80?, 0x92fa20?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:926 +0x236
motadatadatastore/codec.Decoder.decodeZigZagDeltaINT64Values({0xc0000de300?, 0xc0001a9810?, 0xc0000c0fa0?}, {0xc00242a000?, 0x3?, 0x3?}, 0x0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/decoder.go:910 +0x20d
motadatadatastore/codec.TestInvalidDecodeZigZagDeltaINT64(0xc007d8c000?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:6370 +0x176
testing.tRunner(0xc007d8c000, 0xa32458)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:56.549728 PM FATAL [Pool]:INT64 pool -1 not released, reason: INT64 pool leaked
05-June-2025 05:32:56.549803 PM ERROR [Pool]:goroutine 227 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseINT64Pool(0x71f84b4c2000?, 0xc00006ade0?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:666 +0x236
motadatadatastore/codec.Decoder.decodeRLEDeltaINT64Values({0xc00006ade0?, 0xc00006ad20?, 0xc00006ad90?}, {0xc00006acc0, 0x72084?, 0xf?}, 0x0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/decoder.go:1019 +0x23a
motadatadatastore/codec.TestInvalidDecodeRLEDeltaINT64Values(0xc007d8d180)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:6841 +0x4b6
testing.tRunner(0xc007d8d180, 0xa32428)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

05-June-2025 05:32:56.561194 PM FATAL [Pool]:INT64 pool -1 not released, reason: INT64 pool leaked
05-June-2025 05:32:56.561245 PM ERROR [Pool]:goroutine 234 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseINT64Pool(0x71f84b471f30?, 0xc00809c000?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:666 +0x236
motadatadatastore/codec.Decoder.decodeRLEDeltaINT64Values({0xc00809c000?, 0xc000104070?, 0xc000014140?}, {0xc008068070, 0xc006ca1a88?, 0x42e05b?}, 0x0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/decoder.go:1019 +0x23a
motadatadatastore/codec.Decoder.DecodeINT64Values({0xc00809c000?, 0xc000104070?, 0xc000014140?}, 0x4?, 0x30, {0xc008068070?, 0x1?, 0x1?}, {0x0, 0x0}, ...)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/decoder.go:2023 +0x92b
motadatadatastore/codec.TestInvalidDecodeInt64Value(0xc007d8ddc0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/codec/codec_test.go:7292 +0x1349
testing.tRunner(0xc007d8ddc0, 0xa32408)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

