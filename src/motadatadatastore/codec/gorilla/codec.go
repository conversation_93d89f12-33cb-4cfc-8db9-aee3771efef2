/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package gorilla

import (
	"bytes"
	"fmt"
	"math"
	"math/bits"
)

/*
   This code is originally from: https://github.com/dgryski/go-tsz and has been modified to remove
   the timestamp compression functionality.

   It implements the float compression as presented in: http://www.vldb.org/pvldb/vol8/p1816-teller.pdf.
   This implementation uses a sentinel value of NaN which means that float64 NaN cannot be stored using
   this version.
*/

// Note: an uncompressed format is not yet implemented.
// floatCompressedGorilla is a compressed format using the gorilla paper encoding

// uvnan is the constant returned from math.NaN().
const uvnan = 0x7FF8000000000001 //max float64

// Encoder GorillaEncoder encodes multiple float64s into a byte slice.

type Encoder struct {
	writer *bitWriter

	buffer bytes.Buffer

	err error

	value float64

	leading, trailing uint64

	first, finished bool
}

// NewGorillaEncoder returns a new GorillaEncoder.
func NewGorillaEncoder() *Encoder {
	encoder := Encoder{
		first:   true,
		leading: ^uint64(0),
	}

	encoder.writer = newWriter(&encoder.buffer)

	return &encoder
}

// reset sets the encoder back to its initial state.
func (encoder *Encoder) reset() {

	encoder.value = 0

	encoder.err = nil

	encoder.leading = ^uint64(0)

	encoder.trailing = 0

	encoder.buffer.Reset()

	encoder.writer.Resume(0x0, 8)

	encoder.finished = false

	encoder.first = true
}

// bytes returns a Copy of the underlying byte buffer used in the encoder.
func (encoder *Encoder) bytes() ([]byte, error) {

	return encoder.buffer.Bytes(), encoder.err
}

// flush indicates there are no more values to encode.
func (encoder *Encoder) flush() {

	if !encoder.finished {
		// write an end-of-stream record
		encoder.finished = true

		encoder.write(math.NaN())

		encoder.writer.Flush(Zero)
	}
}

func (encoder *Encoder) Encode(values []float64) ([]byte, error) {

	for _, value := range values {

		encoder.write(value)
	}

	encoder.flush()

	bytes, err := encoder.bytes()

	encoder.reset()

	if err != nil {

		return nil, err
	}

	return bytes, nil
}

func (decoder *Decoder) Decode(bytes []byte, padding int, values []float64) ([]float64, error) {

	err := decoder.setBytes(bytes)

	if err != nil {

		return nil, err
	}

	cursor := 0

	for decoder.next() {

		values[cursor] = decoder.getValue()

		cursor++
	}

	return values[:cursor+padding], nil
}

// write encodes value to the underlying buffer.
func (encoder *Encoder) write(value float64) {
	// Only allow NaN as a sentinel value
	if math.IsNaN(value) && !encoder.finished {

		encoder.err = fmt.Errorf("unsupported value: NaN")

		return
	}
	if encoder.first {
		// first point
		encoder.value = value

		encoder.first = false

		encoder.writer.writeBits(math.Float64bits(value), 64)

		return
	}

	delta := math.Float64bits(value) ^ math.Float64bits(encoder.value)

	if delta == 0 {
		encoder.writer.writeBit(Zero)
	} else {
		encoder.writer.writeBit(One)

		leading := uint64(bits.LeadingZeros64(delta))

		trailing := uint64(bits.TrailingZeros64(delta))

		// Clamp number of leading zeros to avoid overflow when encoding
		leading &= 0x1F
		if leading >= 32 {
			leading = 31
		}

		// TODO(dgryski): check if it'encoder 'cheaper' to reset the leading/trailing bits instead
		if encoder.leading != ^uint64(0) && leading >= encoder.leading && trailing >= encoder.trailing {

			encoder.writer.writeBit(Zero)

			encoder.writer.writeBits(delta>>encoder.trailing, 64-int(encoder.leading)-int(encoder.trailing))

		} else {
			encoder.leading, encoder.trailing = leading, trailing

			encoder.writer.writeBit(One)

			encoder.writer.writeBits(leading, 5)

			// Note that if leading == trailing == 0, then signedBits == 64.  But that
			// value doesn't actually fit into the 6 bits we have.
			// Luckily, we never need to encode 0 significant bits, since that would
			// put us in the other case (vdelta == 0).  So instead we write out a 0 and
			// adjust it back to 64 on unpacking.
			signedBits := 64 - leading - trailing

			encoder.writer.writeBits(signedBits, 6)

			encoder.writer.writeBits(delta>>trailing, int(signedBits))
		}
	}

	encoder.value = value
}

// Decoder GorillaDecoder decodes a byte slice into multiple float64 values.
type Decoder struct {
	reader bitReader

	bytes []byte

	err error

	value, leading, trailing uint64

	first, finished bool
}

// setBytes initializes the decoder with bytes. Must call before calling next().
func (decoder *Decoder) setBytes(bytes []byte) error {
	var value uint64

	if len(bytes) == 0 {
		value = uvnan
	} else {
		// first byte is the compression type.
		// we currently just have gorilla compression.
		decoder.reader.Reset(bytes)

		var err error

		value, err = decoder.reader.ReadBits(64)

		if err != nil {
			return err
		}
	}

	// reset all fields.
	decoder.value = value

	decoder.leading = 0

	decoder.trailing = 0

	decoder.bytes = bytes

	decoder.first = true

	decoder.finished = false

	decoder.err = nil

	return nil
}

// next returns true if there are remaining values to read.
func (decoder *Decoder) next() bool {

	if decoder.err != nil || decoder.finished {
		return false
	}

	if decoder.first {
		decoder.first = false

		// mark as finished if there were no values.
		if decoder.value == uvnan { // IsNaN

			decoder.finished = true

			return false
		}

		return true
	}

	// read compressed value
	var bit bool
	if decoder.reader.canReadBitFast() {

		bit = decoder.reader.ReadBitFast()

	} else if v, err := decoder.reader.ReadBit(); err != nil {

		decoder.err = err

		return false
	} else {

		bit = v
	}

	if !bit {
		//decoder.value = decoder.value

	} else {

		var bit bool

		if decoder.reader.canReadBitFast() {

			bit = decoder.reader.ReadBitFast()
		} else if v, err := decoder.reader.ReadBit(); err != nil {
			decoder.err = err

			return false

		} else {

			bit = v
		}

		if !bit {
			// reuse leading/trailing zero bits
			// decoder.leading, decoder.trailing = decoder.leading, decoder.trailing

		} else {

			bits, err := decoder.reader.ReadBits(5)

			if err != nil {

				decoder.err = err

				return false
			}

			decoder.leading = bits

			bits, err = decoder.reader.ReadBits(6)
			if err != nil {

				decoder.err = err

				return false
			}
			usedBits := bits
			// 0 significant bits here means we overflowed and we actually need 64; see comment in encoder
			if usedBits == 0 {

				usedBits = 64
			}
			decoder.trailing = 64 - decoder.leading - usedBits
		}

		usedBits := 64 - decoder.leading - decoder.trailing

		bits, err := decoder.reader.ReadBits(uint(usedBits))

		if err != nil {
			decoder.err = err

			return false
		}

		valueBits := decoder.value

		valueBits ^= bits << decoder.trailing

		if valueBits == uvnan { // IsNaN

			decoder.finished = true

			return false
		}

		decoder.value = valueBits
	}

	return true
}

// getValue returns the current float64 value.
func (decoder *Decoder) getValue() float64 {

	return math.Float64frombits(decoder.value)
}

// Error returns the current decoding error.
//func (decoder *Decoder) Error() error {
//
//	return decoder.err
//}
