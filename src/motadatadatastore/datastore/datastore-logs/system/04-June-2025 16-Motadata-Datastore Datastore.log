04-June-2025 04:19:56.074980 PM FATAL [Datastore]:failed to create store: dummy-datastore-testing-1, reason: failed to open partition reason failed to open the store dummy-datastore-testing-1, reason: error failed to create partition, reason: no decoder for version 131328 registered occurred while opening or creating a store partition 1
04-June-2025 04:19:56.081187 PM ERROR [Datastore]:goroutine 493 [running]:
motadatadatastore/datastore.loadStore({0xc4d000, 0x19}, 0x64, {0xc00014c300, 0xc0001182a0, 0xc0001702d0}, 0xc0000150e0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/datastore.go:637 +0x605
motadatadatastore/datastore.GetStore({0xc4d000, 0x19}, 0x64, 0x0, 0x1, {0xc00014c300, 0xc0001182a0, 0xc0001702d0}, 0xc0000150e0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/datastore.go:701 +0x228
motadatadatastore/datastore.TestCorruptedDatastoreReOpen(0xc0014b9340)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/datastore_test.go:3113 +0xcf2
testing.tRunner(0xc0014b9340, 0xcd42f8)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0x1da
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x90e

04-June-2025 04:19:56.097430 PM FATAL [Datastore]:failed to create store: dummy-datastore-testing-2, reason: failed to open partition reason failed to open the store dummy-datastore-testing-2, reason: error failed to create partition, reason: no decoder for version 131328 registered occurred while opening or creating a store partition 1
04-June-2025 04:19:56.102175 PM ERROR [Datastore]:goroutine 285 [running]:
motadatadatastore/datastore.loadStore({0xc4d019, 0x19}, 0x64, {0xc00014c300, 0xc0001182a0, 0xc0001702d0}, 0xc0000150e0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/datastore.go:637 +0x605
motadatadatastore/datastore.GetStore({0xc4d019, 0x19}, 0x64, 0x0, 0x1, {0xc00014c300, 0xc0001182a0, 0xc0001702d0}, 0xc0000150e0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/datastore.go:701 +0x228
motadatadatastore/datastore.TestCorruptedDatastoreReOpenWithWallFile(0xc000d16540)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/datastore_test.go:3221 +0xc32
testing.tRunner(0xc000d16540, 0xcd4300)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0x1da
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x90e

