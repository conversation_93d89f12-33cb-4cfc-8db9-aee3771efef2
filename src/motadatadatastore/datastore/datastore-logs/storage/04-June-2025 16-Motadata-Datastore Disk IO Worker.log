04-June-2025 04:19:56.103703 PM ERROR [Disk IO Worker]:error runtime error: index out of range [4] with length 0 occurred in io-worker 10
04-June-2025 04:19:56.103859 PM ERROR [Disk IO Worker]:!!!STACK TRACE for io-worker 10!!! 
 goroutine 51 [running]:
motadatadatastore/storage.(*DiskIOWorker).processIOEvent.func1()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/storage/diskioworker_linux.go:285 +0x2e9
panic({0xc1a660?, 0xc001f80030?})
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/runtime/panic.go:787 +0x136
motadatadatastore/storage.getValueBytes({0xc000e50000, 0x0, 0x2710})
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/storage/segment.go:165 +0x1e9
motadatadatastore/storage.read({0x717df410b000, 0x8, 0x7a1a0}, {0xc000e50000, 0x0, 0x2710}, 0xc000fe32c0, 0xc000164360)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/storage/read_linux.go:137 +0x115f
motadatadatastore/storage.(*Partition).get(0xc000fe32c0, {0xc001f80018, 0x16, 0x18}, {0xc000e50000, 0x2710, 0x2710}, 0xc00014c300, 0x0, 0xc000164360)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/storage/partition.go:1534 +0x7ef
motadatadatastore/storage.(*DiskIOWorker).processIOEvent(0xc000164360, 0xc0014e0090)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/storage/diskioworker_linux.go:296 +0x79b
motadatadatastore/storage.(*DiskIOWorker).Start.func1()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/storage/diskioworker_linux.go:124 +0x18f
created by motadatadatastore/storage.(*DiskIOWorker).Start in goroutine 1
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/storage/diskioworker_linux.go:112 +0x67

