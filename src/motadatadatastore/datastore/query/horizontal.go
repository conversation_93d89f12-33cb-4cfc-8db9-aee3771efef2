/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-05-09  			 Hardik Vala			MOTADATA-4911 Query2.0 First Merge
 */

package query

import (
	"context"
	"errors"
	"fmt"
	"github.com/PaesslerAG/gval"
	"github.com/PaesslerAG/jsonpath"
	"github.com/kelindar/bitmap"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"strings"
)

func (worker *Worker) processHorizontalColumnQuery(eventId, executorId int, store *storage.Store) {

	event := worker.WorkerEvents[eventId]

	condition, grouping, found := false, false, false

	dataType := Invalid

	column := utils.Empty

	var err error

	var errs []error

	var valueBuffers [][]byte

	var groups []string

	var groupOrdinals []uint64

	var evaluable gval.Evaluable

	var filterGroups, drillDownFilterGroups []utils.MotadataMap

	operandValues := map[string]interface{}{}

	if len(worker.conditionOperandValues[executorId]) > 0 {

		for key, value := range worker.conditionOperandValues[executorId] {

			operandValues[key] = value
		}
	}

	if len(worker.conditionExpression) > 0 && worker.conditionOperandElementSize > 0 {

		condition = true

		if !worker.bitmapFilter {

			evaluable, err = gval.NewLanguage(append([]gval.Language{gval.Full()}, startWithFunc, endWithFunc, containFunc, containsFunc, jsonpath.Language())...).NewEvaluable(worker.conditionExpression)

			if err != nil {

				worker.logError(fmt.Sprintf("error %v occurred while building gval evaluable language in worker %v event id %v and executor %v", err, worker.workerId, eventId, executorId), true)

				return
			}
		} else {

			filterGroups = worker.filters[executorId].GetMapListValue(Groups)

			drillDownFilterGroups = worker.drillDownFilters[executorId].GetMapListValue(Groups)
		}

	} else if len(worker.ordinals[executorId]) > 0 {

		condition = true
	}

	if worker.groupElementSize > 0 {

		grouping = true

	} else {

		grouping = false
	}

	event.group = utils.Empty

	previousKey := utils.Empty

	key := utils.Empty

	updatePosition := true

	updateRecord := false

	position := worker.memoryPoolPositions[executorId]

	worker.plugin = utils.Empty

	if store.GetDatastoreType() == utils.Log || store.GetDatastoreType() == utils.LogAggregation {

		utils.Split(store.GetName(), utils.HyphenSeparator, worker.tokenizers[0])

		//date-0/1-plugin_id-plugin_category
		worker.plugin = worker.tokenizers[0].Tokens[2] + utils.HyphenSeparator + worker.tokenizers[0].Tokens[3]
	}

	plugin := utils.Empty

	compositeKey := (strings.EqualFold(worker.plugin, datastore.EventSearchPlugin) && store.GetDatastoreType() == utils.Log) || (worker.executors[executorId].queryEngineType == Metric || worker.executors[executorId].queryEngineType == AIOps)

	for keyIndex, adjustedIndex, batch := 0, 1, 0; batch < event.keyElementSize; keyIndex++ {

		if worker.queryAbort {

			break
		}

		worker.keyBuffers[adjustedIndex-1] = []byte(event.keys[keyIndex])

		if adjustedIndex%utils.MaxWorkerEventKeyGroupLength == 0 || keyIndex == event.keyElementSize-1 {

			if worker.executors[executorId].preAggregationJobId == utils.NotAvailable && utils.EnvironmentType != utils.DatastoreBenchIntegrationEnvironment {

				valueBuffers, errs, err = store.GetCacheMultiples(worker.keyBuffers[:adjustedIndex], worker.valueBuffers[:adjustedIndex], worker.encoders[executorId], worker.events, &worker.waitGroup, worker.tokenizers[2])
			} else {

				valueBuffers, errs, err = store.GetMultiples(worker.keyBuffers[:adjustedIndex], worker.valueBuffers[:adjustedIndex], worker.encoders[executorId], worker.events, &worker.waitGroup, worker.tokenizers[2], true)
			}

			if utils.DebugEnabled() {

				workerLogger.Debug(fmt.Sprintf("worker event %v of worker %v fired %v i/o requests (executor %v) (%v)", eventId, worker.workerId, adjustedIndex, executorId, store.GetName()))
			}

			if err != nil {

				worker.logError(fmt.Sprintf("error %v occurred while getting keys :%v", err, worker.keyBuffers), true)

				continue
			}

			for k := 0; k < adjustedIndex; k++ {

				found = false

				utils.Split(event.keys[batch+k], utils.KeySeparator, worker.tokenizers[0])

				key = worker.tokenizers[0].Tokens[0] + utils.KeySeparator + worker.tokenizers[0].Tokens[worker.tokenizers[0].Counts-1]

				if compositeKey {

					key = worker.tokenizers[0].Tokens[0] + utils.KeySeparator + worker.tokenizers[0].Tokens[1] + utils.KeySeparator + worker.tokenizers[0].Tokens[worker.tokenizers[0].Counts-1]
				}

				if key != previousKey {

					if worker.queryAbort {

						break
					}

					worker.tick = StringToINT32(worker.tokenizers[0].Tokens[0])

					worker.part = StringToUINT16(worker.tokenizers[0].Tokens[worker.tokenizers[0].Counts-1])
				}

				plugin = utils.Empty

				worker.compositePlugin = worker.plugin

				if compositeKey {

					plugin = worker.tokenizers[0].Tokens[1]

					worker.compositePlugin = plugin
				}

				blobPoolIndex := utils.NotAvailable

				if errs[k] != nil && strings.EqualFold(errs[k].Error(), utils.ErrorTooLarge) {

					var blobBytes []byte

					blobPoolIndex, blobBytes = worker.memoryPools[executorId].AcquireBlobPool(utils.NotAvailable)

					found, valueBuffers[k], errs[k] = store.Get(worker.keyBuffers[k], blobBytes, worker.encoders[executorId], worker.event, &worker.waitGroup, worker.tokenizers[2], false)

					if !found || len(valueBuffers[k]) == 0 {

						blobPoolIndex = worker.cleanupLargePool(blobPoolIndex, executorId)

						if errs[k] != nil {

							worker.logError(errs[k].Error(), false)
						}

						continue
					}
				}

				if errs[k] == nil && valueBuffers[k] != nil && len(valueBuffers[k]) > 0 {

					found = true

					dataType = GetDataType(valueBuffers[k][0])

				} else if errs[k] != nil {

					if strings.EqualFold(errs[k].Error(), utils.ErrorCorrupted) {

						store.Delete(worker.keyBuffers[k], worker.encoders[executorId], worker.tokenizers[2])

						worker.logError(errs[k].Error(), false)

					} else if !strings.Contains(errs[k].Error(), utils.ErrorIsDeleted) && utils.DebugEnabled() {

						worker.logError(errs[k].Error(), false)
					}
				}

				// means data not found and query is not for drill-down, hence no need to handle missing column scenarios
				if !found && worker.executors[executorId].queryEngineType != DrillDown {

					blobPoolIndex = worker.cleanupLargePool(blobPoolIndex, executorId)

					continue
				}

				if worker.executors[executorId].queryEngineType == Metric || worker.executors[executorId].queryEngineType == AIOps {

					utils.Split(store.GetName(), utils.HyphenSeparator, worker.tokenizers[3])

					column = worker.tokenizers[3].Tokens[2]

				} else if store.GetDatastoreType() == utils.LogAggregation || store.GetDatastoreType() == utils.TrapAggregation || store.GetDatastoreType() == utils.FlowAggregation || store.GetDatastoreType() == utils.PolicyAggregation {

					utils.Split(event.keys[batch+k], utils.KeySeparator, worker.tokenizers[3])

					column = worker.tokenizers[3].Tokens[1]

				} else {

					utils.Split(event.keys[batch+k], utils.KeySeparator, worker.tokenizers[3])

					if !worker.preAggregationQuery && (strings.EqualFold(worker.plugin, datastore.EventSearchPlugin)) {

						column = worker.tokenizers[3].Tokens[2]
					} else {
						column = worker.tokenizers[3].Tokens[1]

					}
				}

				if worker.preAggregatedQuery {

					utils.Split(event.keys[batch+k], utils.KeySeparator, worker.tokenizers[3])

					worker.resetWorkerHorizontalAggregationFuncs()

					if strings.EqualFold(worker.tokenizers[3].Tokens[2], "count") {

						worker.countFunc = true

					} else if strings.EqualFold(worker.tokenizers[3].Tokens[2], "sum") {

						worker.sumFunc = true

					} else if strings.EqualFold(worker.tokenizers[3].Tokens[2], "min") {

						worker.minFunc = true

					} else if strings.EqualFold(worker.tokenizers[3].Tokens[2], "max") {

						worker.maxFunc = true

					} else if strings.EqualFold(worker.tokenizers[3].Tokens[2], "last") {

						worker.lastFunc = true
					}
				}

				worker.aggregationFunc = getAggregationFunction(worker.minFunc, worker.maxFunc, worker.sumFunc, worker.countFunc)

				if !found { // assuming always drill-down query

					blobPoolIndex = worker.cleanupLargePool(blobPoolIndex, executorId)

					if previousKey != key {
						// scenario where either first tick or previous tick not match with current tick and batch size greater than 0 so append that missing values

						if worker.batchSize > 0 {

							for missingColumn := range worker.missingColumns {

								worker.appendMissingValues(worker.batchSize, executorId, event, missingColumn)
							}
						}

						previousKey = key

						// first tick data not found so need to evaluate condition for second tick
						if condition && worker.conditionOperandElementSize > 0 {

							worker.cleanUpConditionContext(executorId, true)

							if err = worker.evaluateConditionExpression(plugin, executorId, store, filterGroups, drillDownFilterGroups, operandValues, evaluable); err != nil {

								worker.batchSize = 0

								updatePosition = true

								clear(worker.missingColumns)

								if utils.DebugEnabled() {

									workerLogger.Debug(fmt.Sprintf("error %v occurred while evaluating condition for tick %v", err, worker.tick))
								}

								blobPoolIndex = worker.cleanupLargePool(blobPoolIndex, executorId)

								worker.cleanUpConditionContext(executorId, true)

								continue
							}
						}

						clear(worker.missingColumns)

						worker.batchSize = 0

						updatePosition = true
					}

					if worker.batchSize > 0 { // first tick with column 1 data found but for column2,3... data not found so append batch size wise missing values

						worker.appendMissingValues(worker.batchSize, executorId, event, column)
					} else {

						worker.missingColumns[column] = struct{}{}
					}

					continue
				}

				if condition && previousKey != key && worker.conditionOperandElementSize > 0 {

					worker.cleanUpConditionContext(executorId, true)

					if err = worker.evaluateConditionExpression(plugin, executorId, store, filterGroups, drillDownFilterGroups, operandValues, evaluable); err != nil {

						previousKey = key

						if utils.DebugEnabled() {

							workerLogger.Debug(fmt.Sprintf("error %v occurred while evaluating condition for tick %v", err, worker.tick))
						}

						blobPoolIndex = worker.cleanupLargePool(blobPoolIndex, executorId)

						worker.cleanUpConditionContext(executorId, true)

						continue
					}
				}

				if grouping && previousKey != key {

					worker.cleanUpConditionContext(executorId, condition && worker.conditionOperandElementSize == 0)

					groups, groupOrdinals, err = worker.getGroups(executorId, store, condition, plugin)

					if err != nil && utils.DebugEnabled() {

						workerLogger.Debug(fmt.Sprintf("error %v occurred while getting groups for tick %v", err, worker.tick))
					}

					if len(groups) == 0 {

						previousKey = key

						blobPoolIndex = worker.cleanupLargePool(blobPoolIndex, executorId)

						event.group = utils.Empty

						worker.cleanUpConditionContext(executorId, true)

						continue
					}

					if worker.columnGroups[executorId].Len()+len(groups) > worker.memoryPools[executorId].GetPoolLength() {

						pendingGroups := worker.columnGroups[executorId].Len()

						for _, group := range groupOrdinals {

							if _, ok := worker.columnGroups[executorId].Get(group); !ok {

								pendingGroups++
							}
						}

						worker.checkOverflow(executorId, pendingGroups)
					}
				}

				if condition && worker.conditionBitmaps[executorId].Count() == 0 {

					blobPoolIndex = worker.cleanupLargePool(blobPoolIndex, executorId)

					continue
				}

				if grouping && len(groups) == 0 {

					blobPoolIndex = worker.cleanupLargePool(blobPoolIndex, executorId)

					continue
				}

				updateRecord = false

				if previousKey != key { // if data found and previous and current ticks are different

					// here batch size is previous ticks length and missing columns are previous ticks missing data might be last column of previous ticks
					if worker.batchSize > 0 && len(worker.missingColumns) > 0 {

						for missingColumn := range worker.missingColumns {

							worker.appendMissingValues(worker.batchSize, executorId, event, missingColumn)
						}
					}

					clear(worker.missingColumns)

					worker.batchSize = 0

					updatePosition = true

					updateRecord = true

					previousKey = key
				}

				if worker.aggregationFunc != 0 { //means

					event.minAggregationColumnIndex, event.maxAggregationColumnIndex, event.sumAggregationColumnIndex, event.countAggregationColumnIndex = worker.getAggregationColumnIndices(worker.aggregationFunc, executorId, column, dataType)
				}

				event.grouping = grouping

				event.condition = condition

				switch {

				case dataType == Float8 || dataType == Float16 || dataType == Float64:

					var values []float64

					index := -1

					if dataType == Float8 {

						index, values, err = worker.decoders[executorId].DecodeFLOAT8Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], event.keys[batch+k], store.GetName(), 0)

					} else if dataType == Float16 {

						index, values, err = worker.decoders[executorId].DecodeFLOAT16Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], event.keys[batch+k], store.GetName(), 0)

					} else {

						index, values, err = worker.decoders[executorId].DecodeFLOAT64Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], event.keys[batch+k], store.GetName(), 0)

					}

					if err != nil {

						worker.logError(fmt.Sprintf(utils.ErrorDecodeValues, event.keys[batch+k], store.GetName(), err), true)

						continue
					}

					if updateRecord {

						worker.records[executorId] += len(values)
					}

					worker.processHorizontalFLOATFunc(executorId, eventId, values, groups, column, dataType)

					if index != utils.NotAvailable {

						worker.memoryPools[executorId].ReleaseFLOAT64Pool(index)
					}

				case dataType == Int8:

					var values []int8

					index := -1

					index, values, err = worker.decoders[executorId].DecodeINT8Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], event.keys[batch+k], store.GetName(), 0)

					if err != nil {

						worker.logError(fmt.Sprintf(utils.ErrorDecodeValues, event.keys[batch+k], store.GetName(), err), true)

						continue
					}

					if updateRecord {

						worker.records[executorId] += len(values)
					}

					updatePosition, position = worker.processHorizontalINT8Func(executorId, eventId, values, groups, column, dataType, updatePosition, position)

					if index != -1 {

						worker.memoryPools[executorId].ReleaseINT8Pool(index)
					}

				case dataType == Int16:

					var values []int16

					index := -1

					index, values, err = worker.decoders[executorId].DecodeINT16Values(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], event.keys[batch+k], store.GetName(), 0)

					if err != nil {

						worker.logError(fmt.Sprintf(utils.ErrorDecodeValues, event.keys[batch+k], store.GetName(), err), true)

						continue
					}

					if updateRecord {

						worker.records[executorId] += len(values)
					}

					updatePosition, position = worker.processHorizontalINT16Func(executorId, eventId, values, groups, column, dataType, updatePosition, position)

					if index != -1 {

						worker.memoryPools[executorId].ReleaseINT16Pool(index)
					}

				case dataType == Int24 || dataType == Int32:

					var values []int32

					index := -1

					index, values, err = worker.decoders[executorId].DecodeINT32Values(GetEncoding(valueBuffers[k][0]), GetDataType(valueBuffers[k][0]), valueBuffers[k][1:], event.keys[batch+k], store.GetName(), 0)

					if err != nil {

						worker.logError(fmt.Sprintf(utils.ErrorDecodeValues, event.keys[batch+k], store.GetName(), err), true)

						continue
					}

					if updateRecord {

						worker.records[executorId] += len(values)
					}

					updatePosition, position = worker.processHorizontalINT32Func(executorId, eventId, values, groups, column, dataType, updatePosition, position)

					if index != -1 {

						worker.memoryPools[executorId].ReleaseINT32Pool(index)
					}

				case dataType == Int40 || dataType == Int48 || dataType == Int56 || dataType == Int64:

					var values []int64

					index := -1

					index, values, err = worker.decoders[executorId].DecodeINT64Values(GetEncoding(valueBuffers[k][0]), GetDataType(valueBuffers[k][0]), valueBuffers[k][1:], event.keys[batch+k], store.GetName(), 0)

					if err != nil {

						worker.logError(fmt.Sprintf(utils.ErrorDecodeValues, event.keys[batch+k], store.GetName(), err), true)

						continue
					}

					if updateRecord {

						worker.records[executorId] += len(values)
					}

					updatePosition, position = worker.processHorizontalINT64Func(executorId, eventId, values, groups, column, dataType, updatePosition, position)

					if index != -1 {

						worker.memoryPools[executorId].ReleaseINT64Pool(index)
					}

				case dataType == String:

					var values []string

					index := -1

					index, values, err = worker.decoders[executorId].DecodeStringValues(GetEncoding(valueBuffers[k][0]), valueBuffers[k][1:], event.keys[batch+k], store.GetName(), 0)

					if err != nil {

						blobPoolIndex = worker.cleanupLargePool(blobPoolIndex, executorId)

						worker.logError(fmt.Sprintf(utils.ErrorDecodeValues, event.keys[batch+k], store.GetName(), err), true)

						continue
					}

					if updateRecord {

						worker.records[executorId] += len(values)
					}

					updatePosition, position = worker.processHorizontalStringFunc(executorId, eventId, values, groups, column, dataType, updatePosition, position)

					if index != -1 {

						worker.memoryPools[executorId].ReleaseStringPool(index)
					}

					blobPoolIndex = worker.cleanupLargePool(blobPoolIndex, executorId)

				case dataType == Blob:

					var blobValues []string

					var values []string

					valuePoolIndex := utils.NotAvailable

					records, batchSize, valuePosition, poolIndex := 0, 0, 0, utils.NotAvailable

					if !worker.lastFunc && worker.aggregationFunc == 0 { // for drill down if first column point is blob need total records for particular key to generate event id and ticks

						for i := 1; i < len(valueBuffers[k]); i = i + 13 {

							index := 0

							batchSize += ReadUINT16Value(valueBuffers[k][i : i+13][:2], &index)
						}

						if worker.batchSize == 0 {

							worker.batchSize = batchSize
						}

						poolIndex, values = worker.memoryPools[executorId].AcquireStringPool(batchSize)
					}

					for bufferIndex := 1; bufferIndex < len(valueBuffers[k]); bufferIndex = bufferIndex + 13 {

						index := 0

						count := ReadUINT16Value(valueBuffers[k][bufferIndex : bufferIndex+13][:2], &index)

						records += count

						startIndex := records - count

						if worker.aggregationFunc == Count || worker.aggregationFunc == SumCount || worker.aggregationFunc == MaxCount || worker.aggregationFunc == MaxSumCount ||
							worker.aggregationFunc == MinCount || worker.aggregationFunc == MinSumCount || worker.aggregationFunc == MinMaxCount || worker.aggregationFunc == MinMaxSumCount {

							if worker.granularity == utils.NotAvailable {

								if grouping && condition {

									size, _ := worker.conditionBitmaps[executorId].Max()

									length := records

									if int(size+1) < records {

										length = int(size + 1)
									}

									for i := startIndex; i < length; i++ {

										if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

											event.group = groups[i]

											event.groupOrdinal = groupOrdinals[i]

											event.countIndex = event.countAggregationColumnIndex

											worker.evaluateCountAggregationFunc(1, eventId, executorId)

										}
									}

								} else if grouping && !condition {

									length := records

									if len(groups) < records {

										length = len(groups)
									}

									for i := startIndex; i < length; i++ {

										event.group = groups[i]

										event.groupOrdinal = groupOrdinals[i]

										event.countIndex = event.countAggregationColumnIndex

										worker.evaluateCountAggregationFunc(1, eventId, executorId)

									}

								} else if !grouping && condition {

									event.countIndex = event.countAggregationColumnIndex

									size, _ := worker.conditionBitmaps[executorId].Max()

									length := records

									if int(size+1) < records {

										length = int(size + 1)
									}

									worker.evaluateCountAggregationFunc(utils.CountIncrementalConditional(worker.conditionBitmaps[executorId], startIndex, length), eventId, executorId)

								} else if !condition && !grouping {

									event.countIndex = event.countAggregationColumnIndex

									worker.evaluateCountAggregationFunc(int64(count), eventId, executorId)
								}
							} else {

								if grouping && condition {

									size, _ := worker.conditionBitmaps[executorId].Max()

									length := records

									if int(size+1) < records {

										length = int(size + 1)
									}

									event.countIndex = event.countAggregationColumnIndex

									for i := startIndex; i < length; i++ {

										if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

											event.group = groups[i]

											event.groupOrdinal = groupOrdinals[i]

											worker.evaluateHorizontalCountHistogramFunc(1, eventId, executorId)

										}
									}

								} else if grouping && !condition {

									length := records

									if len(groups) < records {

										length = len(groups)
									}

									event.countIndex = event.countAggregationColumnIndex

									for i := startIndex; i < length; i++ {

										event.group = groups[i]

										event.groupOrdinal = groupOrdinals[i]

										worker.evaluateHorizontalCountHistogramFunc(1, eventId, executorId)
									}

								} else if !grouping && condition {

									event.countIndex = event.countAggregationColumnIndex

									size, _ := worker.conditionBitmaps[executorId].Max()

									length := records

									if int(size+1) < records {

										length = int(size + 1)
									}

									worker.evaluateHorizontalCountHistogramFunc(utils.CountIncrementalConditional(worker.conditionBitmaps[executorId], startIndex, length), eventId, executorId)

								} else if !condition && !grouping {

									event.countIndex = event.countAggregationColumnIndex

									worker.evaluateHorizontalCountHistogramFunc(int64(count), eventId, executorId)
								}
							}

							continue
						}

						worker.blobEvent.ValueBytes = valueBuffers[k][bufferIndex : bufferIndex+13]

						worker.blobEvent.Encoder = worker.encoders[executorId]

						worker.blobEvent.Decoder = worker.decoders[executorId]

						worker.blobEvent.Tokenizer = worker.tokenizers[2]

						worker.blobEvent.DiskIOEvent = worker.event

						worker.blobEvent.WaitGroup = &worker.waitGroup

						worker.blobEvent.Encoding = GetEncoding(valueBuffers[k][0])

						if len(plugin) > 0 {

							utils.Split(store.GetName(), utils.HyphenSeparator, worker.tokenizers[0])

							worker.tokenizers[0].Tokens[2] = plugin

							storeName := strings.Join(worker.tokenizers[0].Tokens[:worker.tokenizers[0].Counts-1], utils.HyphenSeparator)

							if datastore.IsStoreAvailable(storeName) {

								compositeStore := datastore.GetStore(storeName, utils.Log, false, true, worker.encoders[executorId], worker.tokenizers[2])

								worker.blobEvent.KeyBytes = []byte(datastore.GetKey(worker.tick, column, worker.part))

								worker.blobEvent.Store = compositeStore

								valuePoolIndex, blobValues, err = datastore.GetBlobColumnValues(worker.blobEvent)

							} else {

								err = errors.New(fmt.Sprintf(utils.ErrorAcquireStore, storeName))
							}

						} else {

							worker.blobEvent.KeyBytes = []byte(event.keys[batch+k])

							worker.blobEvent.Store = store

							valuePoolIndex, blobValues, err = datastore.GetBlobColumnValues(worker.blobEvent)
						}

						if err != nil {

							valuePosition = records

							if valuePoolIndex != utils.NotAvailable {

								worker.memoryPools[executorId].ReleaseStringPool(valuePoolIndex)

								valuePoolIndex = utils.NotAvailable
							}

							worker.logError(fmt.Sprintf(utils.ErrorDecodeValues, event.keys[batch+k], store.GetName(), err), true)

							continue
						}

						copy(values[valuePosition:records], blobValues[:count])

						valuePosition = records

						if valuePoolIndex != utils.NotAvailable {

							worker.memoryPools[executorId].ReleaseStringPool(valuePoolIndex)

							valuePoolIndex = utils.NotAvailable
						}
					}

					if worker.granularity == utils.NotAvailable {

						if worker.lastFunc {

							if grouping && condition {

								column += utils.KeySeparator + LastFunc

								event.currentIndex = worker.setColumnContext(executorId, column, String, false)

								tickPoolIndex := worker.setColumnContext(executorId, column+"^tick", Int64, true)

								for i := 0; i < records; i++ {

									if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

										event.group = groups[i]

										event.groupOrdinal = groupOrdinals[i]

										if worker.evaluateHorizontalLastValueTick(tickPoolIndex, eventId, executorId) {

											worker.evaluateLastValueAggregationFuncString(values[0], eventId, executorId)
										}

									}
								}

							} else if grouping && !condition {

								column += utils.KeySeparator + LastFunc

								event.currentIndex = worker.setColumnContext(executorId, column, String, false)

								tickPoolIndex := worker.setColumnContext(executorId, column+"^tick", Int64, true)

								for i := 0; i < records; i++ {

									event.group = groups[i]

									event.groupOrdinal = groupOrdinals[i]

									if worker.evaluateHorizontalLastValueTick(tickPoolIndex, eventId, executorId) {

										worker.evaluateLastValueAggregationFuncString(values[0], eventId, executorId)
									}

								}

							} else if !grouping && condition {

								column += utils.KeySeparator + LastFunc

								event.currentIndex = worker.setColumnContext(executorId, column, String, false)

								tickPoolIndex := worker.setColumnContext(executorId, column+"^tick", Int64, true)

								if worker.evaluateHorizontalLastValueTick(tickPoolIndex, eventId, executorId) {

									worker.evaluateLastValueAggregationFuncString(utils.LastStringConditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)
								}

							} else if !grouping && !condition {

								column += utils.KeySeparator + LastFunc

								event.currentIndex = worker.setColumnContext(executorId, column, String, false)

								tickPoolIndex := worker.setColumnContext(executorId, column+"^tick", Int64, true)

								if worker.evaluateHorizontalLastValueTick(tickPoolIndex, eventId, executorId) {

									worker.evaluateLastValueAggregationFuncString(values[len(values)-1], eventId, executorId)
								}

							}
						}
					} else {

						if worker.aggregationFunc == 0 { // means we need raw data...

							delete(worker.missingColumns, column)

							if !worker.lastFunc && updatePosition {

								updatePosition = false

								worker.memoryPoolPositions[executorId] = position

								if condition {

									position = worker.setHorizontalHistoricalTickColumn(executorId, worker.tick, batchSize, worker.conditionBitmaps[executorId])
								} else {

									position = worker.setHorizontalHistoricalTickColumn(executorId, worker.tick, batchSize, nil)
								}
							}

							if condition {

								worker.setHorizontalHistoricalValueColumnStringConditional(eventId, executorId, column, values, worker.conditionBitmaps[executorId])

							} else {

								worker.setHorizontalHistoricalValueColumnString(eventId, executorId, worker.getBatchSize(len(values), executorId, condition), column, values)

							}

						}
					}

					if poolIndex != utils.NotAvailable {

						worker.memoryPools[executorId].ReleaseStringPool(poolIndex)

						poolIndex = utils.NotAvailable
					}

					if updateRecord {

						worker.records[executorId] += records
					}
				}

			}

			batch += adjustedIndex

			adjustedIndex = 1

			keyIndex = batch - 1

		} else {

			adjustedIndex++
		}
	}

	if !updatePosition || worker.executors[executorId].queryEngineType == DrillDown {

		if worker.batchSize > 0 && len(worker.missingColumns) > 0 {

			for missingColumn := range worker.missingColumns {

				worker.appendMissingValues(worker.batchSize, executorId, event, missingColumn)
			}

			clear(worker.missingColumns)
		}
		worker.memoryPoolPositions[executorId] = position
	}
}

func (worker *Worker) evaluateConditionExpression(plugin string, executorId int, store *storage.Store, filterGroups, drillDownFilterGroups []utils.MotadataMap, operandValues map[string]interface{}, evaluable gval.Evaluable) error {

	length := 0

	dataType := Invalid

	var err error

	var errs []error

	var buffers [][]byte

	dataTypes := make(map[string]byte, worker.conditionOperandElementSize)

	indices := make(map[string]int, worker.conditionOperandElementSize)

	keyBuffers := worker.keyBuffers[utils.MaxWorkerEventKeyGroupLength:]

	valueBuffers := worker.valueBuffers[utils.MaxWorkerEventKeyGroupLength:]

	if len(plugin) == 0 {

		for operandIndex := range worker.conditionOperands[:worker.conditionOperandElementSize] {

			if !datastore.IsSearchableColumn(worker.conditionOperands[operandIndex]) {

				keyBuffers[operandIndex] = []byte(datastore.GetKey(worker.tick, worker.conditionOperands[operandIndex]+utils.OrdinalSuffix, worker.part))
			} else {

				keyBuffers[operandIndex] = []byte(datastore.GetKey(worker.tick, worker.conditionOperands[operandIndex], worker.part))
			}

		}
	} else {

		for operandIndex := range worker.conditionOperands[:worker.conditionOperandElementSize] {

			if !datastore.IsSearchableColumn(worker.conditionOperands[operandIndex]) {

				keyBuffers[operandIndex] = []byte(datastore.GetKey(worker.tick, plugin+utils.KeySeparator+worker.conditionOperands[operandIndex]+utils.OrdinalSuffix, worker.part))

			} else {

				keyBuffers[operandIndex] = []byte(datastore.GetKey(worker.tick, plugin+utils.KeySeparator+worker.conditionOperands[operandIndex], worker.part))
			}

		}
	}

	if worker.executors[executorId].preAggregationJobId == utils.NotAvailable {

		buffers, errs, err = store.GetCacheMultiples(keyBuffers[:worker.conditionOperandElementSize], valueBuffers[:worker.conditionOperandElementSize], worker.encoders[executorId], worker.events, &worker.waitGroup, worker.tokenizers[2])
	} else {

		buffers, errs, err = store.GetMultiples(keyBuffers[:worker.conditionOperandElementSize], valueBuffers[:worker.conditionOperandElementSize], worker.encoders[executorId], worker.events, &worker.waitGroup, worker.tokenizers[2], true)
	}

	if err != nil {

		return err
	}

	key := utils.Empty

	for operandIndex, operand := range worker.conditionOperands[:worker.conditionOperandElementSize] {

		if datastore.IsBlobColumn(operand) {

			key = datastore.GetKey(worker.tick, worker.conditionOperands[operandIndex], worker.part)

		} else {

			key = datastore.GetKey(worker.tick, worker.conditionOperands[operandIndex]+utils.OrdinalSuffix, worker.part)
		}

		blobPoolIndex := utils.NotAvailable

		if errs[operandIndex] != nil {

			dataTypes[operand] = byte(Invalid)

			found := false

			if strings.EqualFold(errs[operandIndex].Error(), utils.ErrorTooLarge) {

				var blobBytes []byte

				blobPoolIndex, blobBytes = worker.decoders[executorId].MemoryPool.AcquireBlobPool(utils.NotAvailable)

				found, buffers[operandIndex], errs[operandIndex] = store.Get(keyBuffers[operandIndex], blobBytes, worker.encoders[executorId], worker.event, &worker.waitGroup, worker.tokenizers[2], false)

			} else if strings.EqualFold(errs[operandIndex].Error(), utils.ErrorCorrupted) {

				store.Delete(keyBuffers[operandIndex], worker.encoders[executorId], worker.tokenizers[2])
			}

			if !found || len(buffers[operandIndex]) == 0 {

				blobPoolIndex = worker.cleanupLargePool(blobPoolIndex, executorId)

				if errs[operandIndex] != nil && utils.DebugEnabled() {

					workerLogger.Debug(fmt.Sprintf(utils.ErrorGetKey, key, store.GetName(), errs[operandIndex]))
				}

				continue
			}
		}

		if buffers[operandIndex] != nil && len(buffers[operandIndex]) > 0 {

			dataType = GetDataType(buffers[operandIndex][0])

		} else {

			continue
		}

		/* only 2 datatypes can be possible here in case of filter

		1. Int24 or INt32 - for ordinals
		2. Blob - for message contains

		*/
		if dataType == Int24 || dataType == Int32 {

			index := -1

			var values []int32

			index, values, err = worker.decoders[executorId].DecodeINT32Values(GetEncoding(buffers[operandIndex][0]), GetDataType(buffers[operandIndex][0]), buffers[operandIndex][1:], key, store.GetName(), 0)

			if err != nil {

				if utils.DebugEnabled() {

					workerLogger.Debug(fmt.Sprintf(utils.ErrorDecodeValues, key, store.GetName(), err))
				}

				continue
			}

			if length == 0 || length > len(values) {

				length = len(values)
			}

			worker.memoryPoolDataTypes[executorId][worker.conditionOperands[operandIndex]] = Int32

			worker.memoryPoolIndices[executorId][worker.conditionOperands[operandIndex]] = index

			dataTypes[operand] = byte(Int32)

			indices[operand] = index

		} else if dataType == Blob {

			if len(buffers[operandIndex][1:])%13 != 0 {

				if utils.DebugEnabled() {

					workerLogger.Debug(fmt.Sprintf(utils.ErrorDecodeValues, key, store.GetName(), err))
				}

				continue
			}

			dataTypes[operand] = byte(Blob)

			count := 0

			for i := 1; i < len(buffers[operandIndex]); i = i + 13 {

				index := 0

				count += ReadUINT16Value(buffers[operandIndex][i : i+13][:2], &index)
			}

			if length == 0 || length > count {

				length = count
			}
		}

		blobPoolIndex = worker.cleanupLargePool(blobPoolIndex, executorId)

	}

	memoryPool := worker.memoryPools[executorId]

	conditionBitmap := &worker.conditionBitmaps[executorId]

	blobPoolIndex := utils.NotAvailable

	defer func() {

		if blobPoolIndex != utils.NotAvailable {

			worker.decoders[executorId].MemoryPool.ReleaseStringPool(blobPoolIndex)

			blobPoolIndex = utils.NotAvailable
		}
	}()

	if worker.bitmapFilter {

		if len(filterGroups) > 0 && (worker.executors[executorId].queryEngineType != Metric && worker.executors[executorId].queryEngineType != AIOps) {

			groupBitmaps := make([]bitmap.Bitmap, len(filterGroups))

			index := 0

			for groupIndex, group := range filterGroups {

				conditions := group.GetMapListValue(Conditions)

				// nested bit maps is used for each group conditions union and intersection
				conditionBitmaps := make([]bitmap.Bitmap, len(conditions))

				var bitmapObj *bitmap.Bitmap

				for conditionIndex, condition := range conditions {

					conditionBitmaps[conditionIndex] = bitmap.Bitmap{0}

					utils.Split(condition.GetStringValue(Operand), utils.KeySeparator, worker.tokenizers[1])

					bitmapObj = worker.executors[executorId].MappingBitmaps[index]

					if bitmapObj != nil && bitmapObj.Count() > 0 {

						for i, value := range memoryPool.GetINT32Pool(indices[worker.tokenizers[1].Tokens[0]])[:length] {

							if bitmapObj.Contains(uint32(value)) {

								conditionBitmaps[conditionIndex].Set(uint32(i))
							}
						}
					}

					index++
				}

				if len(conditions) > 1 {

					// union
					if group.GetStringValue(Operator) == or {

						conditionBitmaps[0].Or(conditionBitmaps[1])

						if len(conditions) == 3 {

							conditionBitmaps[0].Or(conditionBitmaps[2])
						}

					} else { // intersection
						conditionBitmaps[0].And(conditionBitmaps[1])

						if len(conditions) == 3 {

							conditionBitmaps[0].And(conditionBitmaps[2])
						}
					}
				}

				if group.GetStringValue(Filter) == exclude {

					for i := 0; i < length; i++ {

						if conditionBitmaps[0].Contains(uint32(i)) {

							conditionBitmaps[0].Remove(uint32(i))
						} else {
							conditionBitmaps[0].Set(uint32(i))
						}
					}
				}

				groupBitmaps[groupIndex] = conditionBitmaps[0]
			}

			if len(filterGroups) > 1 {

				// union
				if worker.filters[executorId].GetStringValue(Operator) == or {

					for i := 1; i < len(groupBitmaps); i++ {

						groupBitmaps[0].Or(groupBitmaps[i])
					}
				} else { // intersection
					for i := 1; i < len(groupBitmaps); i++ {

						groupBitmaps[0].And(groupBitmaps[i])
					}
				}
			}

			if worker.filters[executorId].GetStringValue(Filter) == exclude {

				for i := 0; i < length; i++ {

					if groupBitmaps[0].Contains(uint32(i)) {

						groupBitmaps[0].Remove(uint32(i))
					} else {

						groupBitmaps[0].Set(uint32(i))
					}
				}
			}

			*conditionBitmap = groupBitmaps[0]

			if conditionBitmap.Count() == 0 {

				return errors.New(utils.ErrorConditionNotQualified)
			}

		} else if worker.executors[executorId].queryEngineType == Metric || worker.executors[executorId].queryEngineType == AIOps {

			operand := worker.conditionOperands[0]

			if operand == utils.EventSource || operand == datastore.Object || operand == utils.ObjectId || operand == utils.NetRouteId {

				bitmapObj := worker.executors[executorId].MappingBitmaps[0]

				if bitmapObj != nil && bitmapObj.Count() > 0 {

					for i, value := range memoryPool.GetINT32Pool(indices[operand])[:length] {

						if bitmapObj.Contains(uint32(value)) {

							conditionBitmap.Set(uint32(i))
						}
					}
				}
			}

			if conditionBitmap.Count() == 0 {

				return errors.New(utils.ErrorConditionNotQualified)
			}
		}

		if len(drillDownFilterGroups) > 0 {

			groupBitmaps := make([]bitmap.Bitmap, len(drillDownFilterGroups))

			index := 0

			for groupIndex, group := range drillDownFilterGroups {

				conditions := group.GetMapListValue(Conditions)

				// nested bit maps is used for each group conditions union and intersection
				conditionBitmaps := make([]bitmap.Bitmap, len(conditions))

				var bitmapObj *bitmap.Bitmap

				for conditionIndex, condition := range conditions {

					conditionBitmaps[conditionIndex] = bitmap.Bitmap{0}

					utils.Split(condition.GetStringValue(Operand), utils.KeySeparator, worker.tokenizers[1])

					if bitmapObj = worker.executors[executorId].DrillDownMappingBitmaps[index]; bitmapObj != nil && bitmapObj.Count() > 0 {

						for i, value := range memoryPool.GetINT32Pool(indices[worker.tokenizers[1].Tokens[0]])[:length] {

							if bitmapObj.Contains(uint32(value)) {

								conditionBitmaps[conditionIndex].Set(uint32(i))
							}
						}
					}

					index++
				}

				if len(conditions) > 1 { // always and condition, so no need to do union

					conditionBitmaps[0].And(conditionBitmaps[1]) // intersection

					if len(conditions) == 3 {

						conditionBitmaps[0].And(conditionBitmaps[2])
					}
				}

				groupBitmaps[groupIndex] = conditionBitmaps[0]
			}

			if len(drillDownFilterGroups) > 1 {

				// always and condition, so no need to do union operation
				for i := 1; i < len(groupBitmaps); i++ {

					groupBitmaps[0].And(groupBitmaps[i])
				}
			}

			if conditionBitmap.Count() == 0 {

				*conditionBitmap = groupBitmaps[0]
			} else {

				conditionBitmap.And(groupBitmaps[0])
			}
		}

	} else {

		var values []string

		position := 0

		invalid := false

		startIndex := 0

		records := 0

		for i := 0; i < length; i++ {

			if invalid {

				break
			}

			for operandIndex, operand := range worker.conditionOperands[:worker.conditionOperandElementSize] {

				dataType = DataType(dataTypes[operand])

				if dataType == Int24 || dataType == Int32 {

					operandValues[operand] = memoryPool.GetINT32Pool(indices[operand])[i]

				} else if dataType == Blob { // as of now we support only one column as a blob column in condition...

					if i == 0 || position >= records {

						position = 0

						records = 0

						if blobPoolIndex != utils.NotAvailable {

							worker.decoders[executorId].MemoryPool.ReleaseStringPool(blobPoolIndex)

							blobPoolIndex = utils.NotAvailable
						}

						// 13 bytes chunk
						// 2 byte total message count
						// 3 byte total message length
						// 8 byte offset

						if i != 0 {

							startIndex += 13
						}

						bytes := buffers[operandIndex][1+startIndex : 1+startIndex+13]

						index := 0

						records += ReadUINT16Value(bytes[:2], &index)

						worker.blobEvent.ValueBytes = bytes

						worker.blobEvent.Encoder = worker.encoders[executorId]

						worker.blobEvent.Decoder = worker.decoders[executorId]

						worker.blobEvent.Tokenizer = worker.tokenizers[2]

						worker.blobEvent.DiskIOEvent = worker.event

						worker.blobEvent.WaitGroup = &worker.waitGroup

						worker.blobEvent.Encoding = GetEncoding(buffers[operandIndex][0])

						if len(plugin) > 0 && (worker.executors[executorId].queryEngineType != Metric && worker.executors[executorId].queryEngineType != AIOps) {

							utils.Split(store.GetName(), utils.HyphenSeparator, worker.tokenizers[1])

							worker.tokenizers[1].Tokens[2] = plugin

							compositeStoreName := strings.Join(worker.tokenizers[1].Tokens[:worker.tokenizers[1].Counts-1], utils.HyphenSeparator)

							if datastore.IsStoreAvailable(compositeStoreName) {

								compositeStore := datastore.GetStore(compositeStoreName, utils.Log, false, true, worker.encoders[executorId], worker.tokenizers[2])

								worker.blobEvent.Store = compositeStore

								worker.blobEvent.KeyBytes = []byte(datastore.GetKey(worker.tick, operand, worker.part))

								blobPoolIndex, values, err = datastore.GetBlobColumnValues(worker.blobEvent)

							} else {

								err = errors.New(fmt.Sprintf(utils.ErrorAcquireStore, compositeStoreName))
							}

						} else {

							worker.blobEvent.Store = store

							worker.blobEvent.KeyBytes = []byte(datastore.GetKey(worker.tick, operand, worker.part))

							blobPoolIndex, values, err = datastore.GetBlobColumnValues(worker.blobEvent)
						}

						if err != nil {

							if utils.DebugEnabled() {

								workerLogger.Debug(fmt.Sprintf("failed to read blob column %v values, reason : %v", operand, err))
							}

							invalid = true

							if blobPoolIndex != utils.NotAvailable {

								worker.decoders[executorId].MemoryPool.ReleaseStringPool(blobPoolIndex)

								blobPoolIndex = utils.NotAvailable
							}

							break
						}
					}

					if len(values) > 0 {

						operandValues[operand] = values[position]

						position++
					}
				}

			}

			result, err := evaluable.EvalBool(context.Background(), operandValues)

			if err != nil && utils.DebugEnabled() {

				workerLogger.Debug(fmt.Sprintf(utils.ErrorConditionExpression, worker.conditionExpression, err))
			}

			if result == true {

				conditionBitmap.Set(uint32(i))
			}
		}

		if blobPoolIndex != utils.NotAvailable {

			worker.decoders[executorId].MemoryPool.ReleaseStringPool(blobPoolIndex)

			blobPoolIndex = utils.NotAvailable
		}
	}

	if conditionBitmap.Count() == 0 {

		return errors.New(utils.ErrorConditionNotQualified)
	}

	return nil

}

func (worker *Worker) getGroups(executorId int, store *storage.Store, condition bool, plugin string) ([]string, []uint64, error) {

	if len(worker.ordinals[executorId]) > 0 {

		condition = false
	}

	var err error

	var errs []error

	var buffers [][]byte

	var groups []string

	var groupOrdinals []uint64

	poolIndex := -1

	size := 0

	records := 0

	storeName := store.GetName()

	keyBuffers := worker.keyBuffers[utils.MaxWorkerEventKeyGroupLength:]

	valueBuffers := worker.valueBuffers[utils.MaxWorkerEventKeyGroupLength:]

	worker.missingGroupColumns[executorId].Clear()

	if len(plugin) == 0 {

		for groupIndex := range worker.groups[:worker.groupElementSize] {

			if _, ok := worker.memoryPoolDataTypes[executorId][worker.groups[groupIndex]]; !ok {

				keyBuffers[groupIndex] = []byte(datastore.GetKey(worker.tick, worker.groups[groupIndex]+utils.OrdinalSuffix, worker.part))

				size++
			}

		}
	} else {

		for groupIndex := range worker.groups[:worker.groupElementSize] {

			if _, ok := worker.memoryPoolDataTypes[executorId][worker.groups[groupIndex]]; !ok {

				keyBuffers[groupIndex] = []byte(datastore.GetKey(worker.tick, plugin+utils.KeySeparator+worker.groups[groupIndex]+utils.OrdinalSuffix, worker.part))

				size++
			}

		}
	}

	if size > 0 {

		if worker.executors[executorId].preAggregationJobId == utils.NotAvailable {

			buffers, errs, err = store.GetCacheMultiples(keyBuffers[:size], valueBuffers[:size], worker.encoders[executorId], worker.events, &worker.waitGroup, worker.tokenizers[2])
		} else {

			buffers, errs, err = store.GetMultiples(keyBuffers[:size], valueBuffers[:size], worker.encoders[executorId], worker.events, &worker.waitGroup, worker.tokenizers[2], true)
		}

		if err != nil {

			return nil, nil, err
		}

	}

	for groupIndex := range worker.groups[:worker.groupElementSize] {

		key := datastore.GetKey(worker.tick, worker.groups[groupIndex]+utils.OrdinalSuffix, worker.part)

		poolIndex = -1

		if _, ok := worker.memoryPoolDataTypes[executorId][worker.groups[groupIndex]]; !ok {

			if buffers[groupIndex] == nil || len(buffers[groupIndex]) == 0 {

				if errs[groupIndex] != nil && utils.DebugEnabled() {

					workerLogger.Debug(fmt.Sprintf(utils.ErrorGetKey, key, storeName, errs[groupIndex]))
				}

				if strings.EqualFold(errs[groupIndex].Error(), utils.ErrorCorrupted) {

					store.Delete(keyBuffers[groupIndex], worker.encoders[executorId], worker.tokenizers[2])
				}

				worker.missingGroupColumns[executorId].Add(groupIndex)

				continue

			}
		} else {

			poolIndex = worker.memoryPoolIndices[executorId][worker.groups[groupIndex]]

			delete(worker.memoryPoolDataTypes[executorId], worker.groups[groupIndex])

			delete(worker.memoryPoolIndices[executorId], worker.groups[groupIndex])
		}

		index := -1

		var values []int32

		if poolIndex != -1 {

			index = poolIndex

			values = worker.memoryPools[executorId].GetINT32Pool(poolIndex)

			defer worker.memoryPools[executorId].ReleaseINT32Pool(index)

		} else {

			index, values, err = worker.decoders[executorId].DecodeINT32Values(GetEncoding(buffers[groupIndex][0]), GetDataType(buffers[groupIndex][0]), buffers[groupIndex][1:], key, storeName, 0)

			if err != nil {

				worker.missingGroupColumns[executorId].Add(groupIndex)

				if utils.DebugEnabled() {

					workerLogger.Error(fmt.Sprintf(utils.ErrorDecodeValues, key, storeName, err))
				}

				continue
			}

			defer worker.memoryPools[executorId].ReleaseINT32Pool(index)

		}

		if condition {

			maxValue, _ := worker.conditionBitmaps[executorId].Max()

			records = int(maxValue + 1)

		} else if records == 0 {

			records = len(values)
		} else {

			records = len(groups)
		}

		if groups == nil {

			groupPoolIndex := -1

			groupPoolIndex, groups = worker.memoryPools[executorId].AcquireStringPool(records)

			worker.memoryPoolDataTypes[executorId]["group"] = String

			worker.memoryPoolIndices[executorId]["group"] = groupPoolIndex

			worker.groupOrdinalPoolIndex, groupOrdinals = worker.memoryPools[executorId].AcquireUINT64Pool(records)
		}

		worker.appendGroups(records, groupIndex, executorId, values, groups, condition)
	}

	if groups == nil || len(groups) == 0 {

		return nil, nil, errors.New("grouping failed")
	}

	if worker.missingGroupColumns[executorId].Len() > 0 {

		// append dummy group columns...
		groupPoolIndex, values := worker.memoryPools[executorId].AcquireStringPool(worker.groupElementSize)

		defer worker.memoryPools[executorId].ReleaseStringPool(groupPoolIndex)

		var tokens []string

		for i := range groups {

			utils.Split(groups[i], utils.GroupSeparator, worker.tokenizers[1])

			tokens = worker.tokenizers[1].Tokens[:worker.tokenizers[1].Counts]

			index := 0

			for j := 0; j < worker.groupElementSize; j++ {

				if !worker.missingGroupColumns[executorId].Has(j) {

					values[j] = tokens[index]

					index++
				} else {

					values[j] = storage.DummyString

					if j == 0 {

						index++
					}
				}

				//delete(worker.missingGroupColumns[executorId], j)

			}

			groups[i] = strings.Join(values, utils.GroupSeparator)
		}
	}

	if len(worker.ordinals[executorId]) > 0 { // means step 2 query either histogram group by or topn sparkline

		conditionBitmap := bitmap.Bitmap{0}

		ordinals := worker.ordinals[executorId]

		for i, group := range groups {

			groupOrdinals[i] = utils.GetHash64([]byte(group))

			if _, ok := ordinals[group]; ok {

				conditionBitmap.Set(uint32(i))
			}
		}

		if worker.conditionBitmaps[executorId].Count() > 0 && conditionBitmap.Count() > 0 {

			worker.conditionBitmaps[executorId].And(conditionBitmap)

		} else {

			worker.conditionBitmaps[executorId] = conditionBitmap
		}

		if worker.conditionBitmaps[executorId].Count() == 0 {

			return nil, nil, errors.New(fmt.Sprintf(utils.ErrorGrouping, utils.ErrorConditionNotQualified))
		}
	} else {

		for i, group := range groups {

			groupOrdinals[i] = utils.GetHash64([]byte(group))
		}
	}

	return groups, groupOrdinals, nil

}

func (worker *Worker) appendGroups(records, groupIndex, executorId int, values []int32, groups []string, condition bool) {

	poolIndex, stringValues := worker.memoryPools[executorId].AcquireStringPool(records)

	defer worker.memoryPools[executorId].ReleaseStringPool(poolIndex)

	INT32ToStringValues(values[:records], stringValues)

	if groupIndex > 0 {

		for i := 0; i < records; i++ {

			if !condition || worker.conditionBitmaps[executorId].Contains(uint32(i)) {

				groups[i] = groups[i] + utils.GroupSeparator + stringValues[i]
			}
		}
	} else {

		for i := 0; i < records; i++ {

			if !condition || worker.conditionBitmaps[executorId].Contains(uint32(i)) {

				groups[i] = stringValues[i]
			}
		}
	}
}
