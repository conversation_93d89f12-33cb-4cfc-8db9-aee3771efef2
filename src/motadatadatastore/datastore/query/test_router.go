/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
	there are two types of router, notification and reader.
	In notification router we get request for widget create , retention job , and backup job from MOTADATA,
	while in reader we get request for query and query abort ( query abort is for long-running queries)
*/

package query

import (
	bytes2 "bytes"
	"encoding/json"
	"fmt"
	"github.com/tidwall/gjson"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/utils"
	"os"
	"runtime"
	"strings"
	"time"
)

var (
	routerLogger = utils.NewLogger("Request Router", "query")

	queueSize = 50
)

const OperationType = 0

const (
	errorQueryRouter = "error %v occurred in query router"

	errorStackTraceQueryRouter = "!!!STACK TRACE for query router!!! \n %v"
)

type RequestRouterType int

type RequestRouter struct {
	memoryPool *utils.MemoryPool

	executors []*Executor

	executorAllocations map[int]QueryEngineType

	Requests chan []byte

	Responses chan string

	ShutdownNotifications chan bool

	queryTrackers map[int64]int64

	drillDownQueries map[int64]int

	drillDownQueryContexts map[int64][]byte

	// query wise sub-queries
	runningQueries map[int64]map[int64]struct{}

	availableExecutors map[int]bool

	idleExecutors []int

	abortTimerThresholdMillis int64

	abortedQueries, aggregationQueries int

	queryLatencies map[QueryEngineType]int64

	queryEngineTypesByQueryId map[int64]QueryEngineType
}

func NewTestRequestRouter(executors []*Executor, executorAllocations map[int]QueryEngineType) *RequestRouter {

	pool := utils.NewMemoryPool(4, utils.MaxPoolLength, false, 0)

	_, err := os.Stat(utils.JobDir)

	if os.IsNotExist(err) {

		_ = os.Mkdir(utils.JobDir, 0755)
	}

	var queryExecutors map[int]bool

	var runningQueries map[int64]map[int64]struct{}

	var drillDownQueryContexts map[int64][]byte

	var drillDownQueries map[int64]int

	var queryTrackers map[int64]int64

	var idleExecutors []int

	queryExecutors = make(map[int]bool, utils.QueryExecutors)

	runningQueries = make(map[int64]map[int64]struct{}, queueSize*5)

	drillDownQueryContexts = make(map[int64][]byte, 1000)

	drillDownQueries = make(map[int64]int, 1000)

	queryTrackers = make(map[int64]int64, 1000)

	idleExecutors = make([]int, utils.QueryExecutors)

	return &RequestRouter{

		executorAllocations: executorAllocations,

		executors: executors,

		Requests: make(chan []byte, utils.GetDatastoreEventChannelSize()),

		Responses: make(chan string, utils.QueryExecutors),

		ShutdownNotifications: make(chan bool, 5),

		memoryPool: pool,

		availableExecutors: queryExecutors,

		runningQueries: runningQueries,

		drillDownQueryContexts: drillDownQueryContexts,

		drillDownQueries: drillDownQueries,

		queryTrackers: queryTrackers,

		idleExecutors: idleExecutors,

		abortTimerThresholdMillis: int64(utils.GetQueryAbortTimerSeconds() * 1000),

		queryEngineTypesByQueryId: make(map[int64]QueryEngineType),

		queryLatencies: make(map[QueryEngineType]int64),
	}

}

func (router *RequestRouter) Start() {

	go func() {

		router.startReader()

	}()
}

func (router *RequestRouter) startReader() {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			routerLogger.Error(fmt.Sprintf(errorQueryRouter, err))

			routerLogger.Error(fmt.Sprintf(errorStackTraceQueryRouter, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			utils.ShutdownNotifications <- "Router" + utils.GroupSeparator + "Shutting down query router" + utils.GroupSeparator + fmt.Sprintf("%v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))

			return
		}
	}()

	for index := 0; index < utils.QueryExecutors; index++ {

		router.availableExecutors[index] = true
	}

	tokenizer := &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	queryAbortTimer := time.NewTicker(time.Second * time.Duration(30))

	for {

		select {

		case <-queryAbortTimer.C:

			router.abort()

		case bytes := <-router.Requests:

			router.routeReadRequest(bytes)

		case response := <-router.Responses:

			router.processQueuedQuery(response, tokenizer)

		case <-router.ShutdownNotifications:

			routerLogger.Info("shutting down query request router...")

			return
		}
	}
}

func (router *RequestRouter) routeReadRequest(bytes []byte) {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			routerLogger.Error(fmt.Sprintf(errorQueryRouter, err))

			routerLogger.Error(fmt.Sprintf(errorStackTraceQueryRouter, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		}
	}()

	if len(bytes) > 0 {

		context := utils.MotadataMap{}

		gjson.ParseBytes(bytes[1:]).ForEach(func(key, value gjson.Result) bool {
			context[key.String()] = value.Value()
			return true
		})

		if utils.DebugEnabled() {

			if context.Contains(utils.AggregationJobId) {

				routerLogger.Debug(fmt.Sprintf("query request received from %v...", context.GetIntValue(utils.AggregationJobId)))

			} else {

				routerLogger.Debug("query request received...")
			}
		}

		queryId := context.GetInt64Value(QueryId)

		subQueryId := context.GetInt64Value(SubQueryId)

		queryEngineType := getQueryEngineTypeV2(context)

		priority := int(utils.P0)

		if context.Contains(utils.QueryPriority) {

			priority = context.GetIntValue(utils.QueryPriority)
		}

		if priority == int(utils.P1) {

			router.aggregationQueries += 1

		}

		idleExecutors := router.getIdleExecutors(queryEngineType)

		if queryEngineType == DrillDown {

			subQueryId = queryId

			if utils.DebugEnabled() {

				routerLogger.Debug(fmt.Sprintf("pending drilldown query contexts %v", len(router.drillDownQueryContexts)))
			}
		}

		if _, ok := router.runningQueries[queryId]; !ok {

			router.runningQueries[queryId] = map[int64]struct{}{}
		}

		router.runningQueries[queryId][subQueryId] = struct{}{}

		if idleExecutors > 0 {

			router.sendQuery(context, router.idleExecutors[0], priority, queryEngineType)

		} else {

			fmt.Println("hello")
		}
	}

}

func (router *RequestRouter) processQueuedQuery(response string, tokenizer *utils.Tokenizer) {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			routerLogger.Error(fmt.Sprintf(errorQueryRouter, err))

			routerLogger.Error(fmt.Sprintf(errorStackTraceQueryRouter, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		}
	}()

	utils.Split(response, utils.KeySeparator, tokenizer)

	if tokenizer.Counts > 1 {

		router.availableExecutors[codec.StringToINT(tokenizer.Tokens[1])] = true

		if utils.DebugEnabled() {

			routerLogger.Debug(fmt.Sprintf("executor %v is available...", codec.StringToINT(tokenizer.Tokens[1])))
		}

		queryId := codec.StringToINT64(tokenizer.Tokens[2])

		subQueryId := codec.StringToINT64(tokenizer.Tokens[3])

		if _, ok := router.runningQueries[queryId]; ok {

			delete(router.runningQueries[queryId], subQueryId)

			if len(router.runningQueries[queryId]) == 0 {

				delete(router.runningQueries, queryId)

				if _, ok = router.queryTrackers[queryId]; ok {

					router.queryLatencies[router.queryEngineTypesByQueryId[queryId]] += time.Now().UnixMilli() - router.queryTrackers[queryId]

				}

				// if query is not drill down and all sub queries job finished delete query from query abort time tracker..
				if _, ok = router.drillDownQueries[queryId]; !ok {

					delete(router.queryTrackers, queryId)
				}

				delete(router.queryEngineTypesByQueryId, queryId)
			}
		}

	}
}

func (router *RequestRouter) getIdleExecutors(datastoreType QueryEngineType) int {

	size := 0

	for executor, available := range router.availableExecutors {

		if router.executorAllocations[executor] == datastoreType && available {

			router.idleExecutors[size] = executor

			size++
		}
	}

	return size

}

func (router *RequestRouter) qualifyDrillDownEvent(context utils.MotadataMap) {

	queryId := context.GetInt64Value(QueryId)

	if _, ok := router.drillDownQueryContexts[queryId]; ok {

		bytes := make([]byte, len(router.drillDownQueryContexts[queryId]))

		copy(bytes, router.drillDownQueryContexts[queryId])

		context[DrillDownQueryContext] = bytes

	} else {

		eventId := 0

		for _, value := range router.drillDownQueries {

			if eventId < value {

				eventId = value
			}
		}

		eventId++

		if eventId == utils.DrillDownQueryExecutors*utils.MaxDrillDownEvents {

			eventId = 0
		}

		router.drillDownQueries[queryId] = eventId
	}

	context[DrillDownEventId] = router.drillDownQueries[queryId]
}

func (router *RequestRouter) sendQuery(context utils.MotadataMap, executor, priority int, queryEngineType QueryEngineType) {

	router.availableExecutors[executor] = false

	if utils.DebugEnabled() {

		routerLogger.Debug(fmt.Sprintf("query assigned to executor %v", executor))
	}

	if queryEngineType == DrillDown {

		router.qualifyDrillDownEvent(context)
	}

	queryId := context.GetInt64Value(QueryId)

	subQueryId := context.GetInt64Value(SubQueryId)

	if priority == int(utils.P1) { // do not track aggregation query timer..

		router.executors[executor].Requests <- context

	} else {

		router.queryTrackers[queryId] = time.Now().UnixMilli()

		router.queryEngineTypesByQueryId[queryId] = queryEngineType

		router.executors[executor].Requests <- context

		buffer := &bytes2.Buffer{}

		//first 2 bytes length
		buffer.WriteByte(byte(len(utils.DatastoreQueryAck)))

		buffer.WriteByte(byte(len(utils.DatastoreQueryAck) >> 8))

		buffer.Write([]byte(utils.DatastoreQueryAck))

		codec.EncodeINT64Value(queryId, buffer)

		codec.EncodeINT64Value(subQueryId, buffer)

		utils.PublisherResponses <- buffer.Bytes()
	}
}

func (router *RequestRouter) abort() {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			routerLogger.Error(fmt.Sprintf(errorQueryRouter, err))

			routerLogger.Error(fmt.Sprintf(errorStackTraceQueryRouter, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		}
	}()

	for queryId, timestamp := range router.queryTrackers {

		if time.Now().UnixMilli()-timestamp >= router.abortTimerThresholdMillis {

			router.abortedQueries += 1

			router.queryLatencies[router.queryEngineTypesByQueryId[queryId]] += time.Now().UnixMilli() - timestamp

			delete(router.queryTrackers, queryId)

			delete(router.queryEngineTypesByQueryId, queryId)

			buffer := &bytes2.Buffer{}

			buffer.WriteByte(utils.QueryAbort)

			context := make(utils.MotadataMap)

			context[QueryId] = queryId

			context[datastore.Status] = utils.Aborted

			bytes, _ := json.Marshal(&context)

			buffer.Write(bytes)

			utils.Requests <- append([]byte{utils.QueryAbort}, buffer.Bytes()...)
		}
	}
}

func getQueryEngineTypeV2(context utils.MotadataMap) (queryEngineType QueryEngineType) {

	datastoreType := utils.DatastoreType(context.GetMapValue(VisualizationDataSources).GetIntValue(utils.Type))

	category := context.GetStringValue(VisualizationCategory)

	if category == Anomaly || category == Forecast || category == Baseline {

		return AIOps

	} else if context.Contains(utils.DrillDown) && strings.EqualFold(context.GetStringValue(utils.DrillDown), utils.Yes) {

		if datastoreType == utils.PerformanceMetric || datastoreType == utils.ObjectStatusMetric || datastoreType == utils.TrapFlapHistory {

			return Metric
		}

		return DrillDown

	} else if datastoreType == utils.PerformanceMetric || datastoreType == utils.ObjectStatusMetric || datastoreType == utils.TrapFlapHistory {

		return Metric

	} else if datastoreType == utils.Flow {

		return Flow

	}

	return Log
}
