/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-05-09  			 Hardik Vala			MOTADATA-4911 Query2.0 First Merge
* 2025-06-03			 <PERSON><PERSON><PERSON>-6393 Refactored waitgroup add count in case of drilldown query
* 2025-06-23             Vedant Dokania         Motadata-6370 Mapping operand changes to get the instance type store
 */

package query

import (
	"errors"
	"fmt"
	"github.com/kelindar/bitmap"
	"math"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"strings"
	"time"
)

func (executor *Executor) distributeWorkerEvents() error {

	executor.pendingEvents = executor.qualifiedStoreElementSize - executor.currentStoreIndex

	if utils.QueryPlanLogging {

		if executor.preAggregationQuery {

			executor.logQueryPlan(fmt.Sprintf("qualified aggregation worker events are %v", executor.pendingEvents))
		} else {

			executor.logQueryPlan(fmt.Sprintf("qualified raw worker events are %v", executor.pendingEvents))
		}

	}

	if executor.pendingEvents == 0 {

		return errors.New(fmt.Sprintf(utils.ErrorKeyQualification, "qualified key element size is zero"))
	}

	if executor.currentStoreIndex == 0 && executor.externalGroupIndex >= 0 {

		if !executor.bitmapFilter {

			// need to prepare gval condition for qualified entities

			if len(executor.conditionExpression) > 0 {

				executor.conditionExpression += andCondition + startBracket + "contains" + startBracket + "$[\"" + executor.conditionOperand + "\"]" + utils.CommaSeparator + datastore.Objects + endBracket + endBracket
			} else {

				executor.conditionExpression = "contains" + startBracket + "$[\"" + executor.conditionOperand + "\"]" + utils.CommaSeparator + datastore.Objects + endBracket
			}
		}

		if executor.preAggregationQuery || (executor.queryEngineType != Metric && executor.queryEngineType != AIOps) {

			store := datastore.GetStore(executor.mappingOperand+utils.HyphenSeparator+datastore.Mappings, utils.None, false, true, executor.encoder, executor.tokenizers[0])

			if store == nil {

				return errors.New(fmt.Sprintf(utils.ErrorAcquireStore, executor.mappingOperand+utils.HyphenSeparator+datastore.Mappings))
			}

			dataType := String

			if executor.conditionOperand == utils.ObjectId {

				dataType = Int64
			}

			groupOrdinals := map[string]utils.MotadataMap{}

			if dataType == String {

				poolIndex, entities := executor.memoryPool.AcquireStringPool(len(executor.externalGroupFilterOrdinals))

				elementSize := 0

				for entity := range executor.externalGroupFilterOrdinals {

					entities[elementSize] = entity

					elementSize++
				}

				_, mapperPoolIndex, ordinals := store.MapStringValues(poolIndex, executor.encoder, elementSize)

				if executor.queryEngineType == Metric || executor.queryEngineType == AIOps {

					executor.MappingBitmaps[0] = &bitmap.Bitmap{0}

					for _, ordinal := range ordinals {

						executor.MappingBitmaps[0].Set(uint32(ordinal))
					}
				}

				externalGroupFilters := map[string]*CompactedBitmap{}

				conditionOperandValues := utils.MotadataMap{}

				for index := range ordinals {

					ordinal := INT32ToStringValue(ordinals[index])

					conditionOperandValues[ordinal] = struct{}{}

					compactedBitmap := executor.externalGroupFilterOrdinals[entities[index]]

					compactedBitmap.Iterate(func(group uint32) {

						if _, ok := groupOrdinals[executor.externalGroupFilters[group]]; !ok {

							groupOrdinals[executor.externalGroupFilters[group]] = utils.MotadataMap{}
						}
						groupOrdinals[executor.externalGroupFilters[group]][ordinal] = struct{}{}
					})

					externalGroupFilters[ordinal] = compactedBitmap
				}

				executor.conditionOperandValues[datastore.Objects] = conditionOperandValues

				clear(executor.externalGroupFilterOrdinals)

				for entity, groups := range externalGroupFilters {

					executor.externalGroupFilterOrdinals[entity] = groups
				}

				executor.memoryPool.ReleaseStringPool(poolIndex)

				executor.memoryPool.ReleaseINT32Pool(mapperPoolIndex)

			} else {

				poolIndex, entities := executor.memoryPool.AcquireINT64Pool(len(executor.externalGroupFilterOrdinals))

				elementSize := 0

				for entity := range executor.externalGroupFilterOrdinals {

					entities[elementSize] = StringToINT64(entity)

					elementSize++
				}

				_, mapperPoolIndex, ordinals := store.MapNumericValues(poolIndex, executor.encoder, elementSize)

				if executor.queryEngineType == Metric || executor.queryEngineType == AIOps {

					executor.MappingBitmaps[0] = &bitmap.Bitmap{0}

					for _, ordinal := range ordinals {

						executor.MappingBitmaps[0].Set(uint32(ordinal))
					}
				}

				externalGroupFilters := map[string]*CompactedBitmap{}

				conditionOperandValues := utils.MotadataMap{}

				for index := range ordinals {

					ordinal := INT32ToStringValue(ordinals[index])

					conditionOperandValues[ordinal] = struct{}{}

					compactedBitmap := executor.externalGroupFilterOrdinals[INT64ToStringValue(entities[index])]

					compactedBitmap.Iterate(func(group uint32) {

						if _, ok := groupOrdinals[executor.externalGroupFilters[group]]; !ok {

							groupOrdinals[executor.externalGroupFilters[group]] = utils.MotadataMap{}
						}
						groupOrdinals[executor.externalGroupFilters[group]][ordinal] = struct{}{}
					})

					externalGroupFilters[ordinal] = compactedBitmap
				}

				executor.conditionOperandValues[datastore.Objects] = conditionOperandValues

				clear(executor.externalGroupFilterOrdinals)

				for entity, groups := range externalGroupFilters {

					executor.externalGroupFilterOrdinals[entity] = groups
				}

				executor.memoryPool.ReleaseINT64Pool(poolIndex)

				executor.memoryPool.ReleaseINT32Pool(mapperPoolIndex)
			}

			if len(executor.ordinals) > 0 && executor.externalGroupIndex >= 0 {

				for group := range executor.ordinals {

					utils.Split(group, utils.GroupSeparator, executor.tokenizers[0])

					if ordinals, ok := groupOrdinals[executor.tokenizers[0].Tokens[executor.externalGroupIndex]]; ok {

						if executor.tokenizers[0].Counts > 1 {

							for ordinal := range ordinals {

								executor.tokenizers[0].Tokens[executor.externalGroupIndex] = ordinal

								executor.ordinals[strings.Join(executor.tokenizers[0].Tokens[:executor.tokenizers[0].Counts], utils.GroupSeparator)] = struct{}{}
							}
						} else {

							for ordinal := range ordinals {

								executor.ordinals[ordinal] = struct{}{}
							}
						}

						delete(executor.ordinals, group)
					}
				}
			}
		}

	}

	var dataFilter, drillDownFilter utils.MotadataMap

	var conditionOperands []string

	conditionOperandElementSize := 0

	if executor.preAggregationQuery && (executor.queryEngineType == Metric || executor.queryEngineType == AIOps) {

		clear(executor.conditionOperands)

		executor.conditionOperands[executor.conditionOperand] = struct{}{}
	}

	if !executor.preAggregationQuery && (executor.queryEngineType != Metric && executor.queryEngineType != AIOps) && executor.externalGroupIndex >= 0 { // external grouping

		if _, ok := executor.conditionOperands[executor.conditionOperand]; ok {

			conditionOperands = make([]string, len(executor.conditionOperands))

		} else {

			conditionOperands = make([]string, 1+len(executor.conditionOperands))

			conditionOperands[conditionOperandElementSize] = executor.conditionOperand

			conditionOperandElementSize++
		}

	} else {

		conditionOperands = make([]string, len(executor.conditionOperands))
	}

	for conditionOperand := range executor.conditionOperands {

		conditionOperands[conditionOperandElementSize] = conditionOperand

		conditionOperandElementSize++
	}

	if len(conditionOperands) > 0 {

		filters := executor.request.GetMapValue(VisualizationDataSources).GetMapValue(Filters)

		dataFilter = filters.GetMapValue(DataFilter)

		drillDownFilter = filters.GetMapValue(DrillDownFilter)
	}

	executor.qualifyWorkers(conditionOperands, dataFilter, drillDownFilter)

	return nil
}

func (executor *Executor) qualifyWorkers(conditionOperands []string, dataFilter, drillDownFilter utils.MotadataMap) {

	workerEvents := int(math.Ceil(float64(executor.pendingEvents) / float64(utils.QueryWorkers)))

	if utils.QueryPlanLogging {

		executor.logQueryPlan(fmt.Sprintf("per worker qualified worker events are %v", workerEvents))
	}

	if workerEvents > utils.MaxWorkerEvents {

		workerEvents = utils.MaxWorkerEvents

		if utils.QueryPlanLogging {

			executor.logQueryPlan("per worker worker events exhausted")
		}
	}

	workerId := executor.minWorkerId

	maxWorkerEvents := utils.Workers * utils.MaxWorkerEvents

	qualifiedWorkerEvents := 0

	eventId := 0

	found := false

	// redistribute to leverage multiple workers
	if executor.queryEngineType != DrillDown && ((executor.qualifiedStoreElementSize > 1 || (executor.keyElementSize/executor.probes) > 10) && float64(executor.qualifiedStoreElementSize) < 0.8*float64(utils.Workers)) {

		executor.redistribute()
	}

	start := executor.currentStoreIndex

	for index := executor.currentStoreIndex; index < executor.qualifiedStoreElementSize; index++ {

		eventId, found = executor.workerPendingEvents.Get(workerId)

		if !found {

			if workerId != executor.maxWorkerId { // current executor last worker is already utilized by other executor hence skip it

				executor.availableWorkers[workerId].Store(true)

			} else if !executor.availableWorkers[workerId].CompareAndSwap(false, true) {

				break
			}

			eventId = 0

			executor.setWorkerContext(executor.workers[workerId], conditionOperands, dataFilter, drillDownFilter)
		}

		executor.setWorkerEventContext(index, eventId, executor.workers[workerId])

		qualifiedWorkerEvents++

		executor.workerPendingEvents.Put(workerId, eventId+1)

		workerId++

		if workerId > executor.maxWorkerId {

			workerId = executor.minWorkerId
		}

		if qualifiedWorkerEvents == maxWorkerEvents {

			break
		}
	}

	executor.currentStoreIndex += qualifiedWorkerEvents

	if executor.pendingEvents > qualifiedWorkerEvents {

		// borrow other executors worker

		pendingEvents := executor.pendingEvents - qualifiedWorkerEvents

		for i := 0; i < utils.QueryExecutors && pendingEvents > 0; i++ {

			if i == executor.executorId {

				continue
			}

			workerId = (i * utils.Workers) + (utils.Workers - 1)

			if executor.availableWorkers[workerId].CompareAndSwap(false, true) {

				eventId = 0

				executor.setWorkerContext(executor.workers[workerId], conditionOperands, dataFilter, drillDownFilter)

				for j := 0; j < utils.MaxWorkerEvents; j++ {

					if utils.QueryPlanLogging {

						executor.logQueryPlan(fmt.Sprintf("executor %v borrowed worker %v , worker event %v to complete query", executor.executorId, workerId, eventId))
					}

					executor.setWorkerEventContext(executor.currentStoreIndex, eventId, executor.workers[workerId])

					executor.currentStoreIndex++

					eventId++

					executor.workerPendingEvents.Put(workerId, eventId)

					pendingEvents--

					if pendingEvents == 0 {

						break
					}
				}
			}
		}
	}

	if utils.QueryPlanLogging {

		for worker, events := range executor.workerPendingEvents.All() {

			executor.logQueryPlan(fmt.Sprintf("qualified worker %v , worker events %v", worker, events))
		}
	}

	if executor.queryEngineType == DrillDown {

		executor.waitGroup.Add(executor.qualifiedStoreElementSize - start)
	}
}

func (executor *Executor) redistribute() {

	qualifiedStorePoolIndex, qualifiedStores := executor.memoryPool.AcquireStringPool(executor.qualifiedStoreElementSize * 2)

	keyIndex, keyIndices := executor.memoryPool.AcquireINTPool(executor.qualifiedStoreElementSize * 4)

	defer executor.memoryPool.ReleaseStringPool(qualifiedStorePoolIndex)

	defer executor.memoryPool.ReleaseINTPool(keyIndex)

	keyElementSize := 0

	qualifiedStoreElementSize := 0

	for i := 0; i < executor.qualifiedStoreElementSize; i++ {

		start := executor.keyIndices[i*2]

		end := executor.keyIndices[(i*2)+1]

		if end-start > executor.probes {

			keyIndices[keyElementSize] = start

			keyElementSize++

			mid := (end - start) / 2

			keyIndices[keyElementSize] = start + (mid - (mid % executor.probes))

			start = keyIndices[keyElementSize]

			keyElementSize++

			qualifiedStores[qualifiedStoreElementSize] = executor.qualifiedStores[i]

			qualifiedStoreElementSize++

			keyIndices[keyElementSize] = start

			keyElementSize++

			keyIndices[keyElementSize] = end

			keyElementSize++

			qualifiedStores[qualifiedStoreElementSize] = executor.qualifiedStores[i]

			qualifiedStoreElementSize++

		} else {

			keyIndices[keyElementSize] = start

			keyElementSize++

			keyIndices[keyElementSize] = end

			keyElementSize++

			qualifiedStores[qualifiedStoreElementSize] = executor.qualifiedStores[i]

			qualifiedStoreElementSize++
		}
	}

	copy(executor.qualifiedStores, qualifiedStores[:qualifiedStoreElementSize])

	executor.qualifiedStoreElementSize = qualifiedStoreElementSize

	copy(executor.keyIndices, keyIndices[:keyElementSize])

	executor.pendingEvents = executor.qualifiedStoreElementSize
}

func (executor *Executor) notifyWorkers() {

	executor.logQueryPlan("worker notifying started...")

	// list of total qualified worker events as per worker,
	for worker, pendingEvents := range executor.workerPendingEvents.All() {

		if pendingEvents > 0 {

			executor.workerTimeTrackers.Put(worker, time.Now().UnixMilli())

			if utils.QueryPlanLogging {

				executor.logQueryPlan(fmt.Sprintf("assigning %v worker events to worker %v", pendingEvents, worker))
			}

			for index := 0; index < pendingEvents; index++ {

				//eventId ^ executorId
				executor.workers[worker].Requests <- executor.subQueryId + utils.KeySeparator + INTToStringValue(index) + utils.KeySeparator + INTToStringValue(executor.executorId)
			}
		}
	}
}

func (executor *Executor) setWorkerContext(worker *Worker, conditionOperands []string, dataFilter, drillDownFilter utils.MotadataMap) {

	executor.usedWorkers[executor.usedWorkerElementSize] = worker.workerId

	executor.usedWorkerElementSize++

	worker.resetWorkerHorizontalAggregationFuncs()

	worker.groupElementSize = 0

	worker.conditionExpression = executor.conditionExpression

	if executor.datastoreType == utils.ObjectStatusFlapMetric {

		executor.workers[worker.workerId].maxHistoricalRecords = utils.MaxStatusFlapHistoricalRecords
	} else {

		maxHistoricalRecords := utils.MaxHistoricalRecords

		if executor.request.Contains(utils.MaxRecords) {

			maxHistoricalRecords = executor.request.GetIntValue(utils.MaxRecords)
		}

		executor.workers[worker.workerId].maxHistoricalRecords = maxHistoricalRecords * 2

	}

	if len(executor.externalGroupFilterOrdinals) > 0 {

		for key, compactedBitmap := range executor.externalGroupFilterOrdinals {

			worker.externalGroupFilterOrdinals[key] = compactedBitmap
		}
	}

	if executor.GroupColumnElementSize > 0 {

		if executor.preAggregationQuery && (executor.queryEngineType == Metric || executor.queryEngineType == AIOps) {

			worker.groups[worker.groupElementSize] = executor.conditionOperand

			worker.groupElementSize++
		} else {

			for _, group := range executor.GroupColumns[:executor.GroupColumnElementSize] {

				if group != Tag && group != Group {

					worker.groups[worker.groupElementSize] = group

					worker.groupElementSize++
				} else {

					worker.groups[worker.groupElementSize] = executor.conditionOperand

					worker.groupElementSize++
				}
			}
		}
	}

	if len(executor.aggregations) > 0 {

		for aggregation := range executor.aggregations {

			worker.setAggregationFunc(aggregation)
		}
	}

	if len(conditionOperands) > 0 {

		worker.conditionOperands = conditionOperands

		worker.conditionOperandElementSize = len(conditionOperands)

		if len(executor.conditionOperandValues) > 0 {

			for condition, value := range executor.conditionOperandValues {

				executor.workers[worker.workerId].conditionOperandValues[executor.executorId][condition] = value
			}
		}

		if len(dataFilter) > 0 {

			executor.workers[worker.workerId].filters[executor.executorId] = dataFilter.Clone()
		}

		if len(drillDownFilter) > 0 {

			executor.workers[worker.workerId].drillDownFilters[executor.executorId] = drillDownFilter.Clone()
		}
	} else {

		worker.conditionOperandElementSize = 0
	}

	if executor.ordinals != nil && len(executor.ordinals) > 0 {

		for ordinal := range executor.ordinals {

			executor.workers[worker.workerId].ordinals[executor.executorId][ordinal] = struct{}{}
		}
	}

	worker.externalGrouping = executor.GroupColumnElementSize > 0 && executor.externalGroupIndex >= 0

	worker.externalInstanceGrouping = executor.externalInstanceGrouping

	worker.fromTimeTick = utils.UnixToSeconds(executor.fromDateTime.Unix())

	worker.toTimeTick = utils.UnixToSeconds(executor.toDateTime.Unix())

	worker.startTick = executor.startTick

	worker.endTick = executor.endTick

	worker.granularity = int(executor.granularity)

	worker.timeBoundQuery = executor.timeBoundQuery

	worker.preAggregatedQuery = executor.preAggregationQuery

	worker.innerGroupingField = executor.innerGroupingField

	worker.outerGroupingField = executor.outerGroupingField

	worker.preAggregationQuery = executor.preAggregationQuery

	worker.bitmapFilter = executor.bitmapFilter
}

func (executor *Executor) setWorkerEventContext(index, eventId int, worker *Worker) {

	utils.Split(executor.qualifiedStores[index], utils.KeySeparator, executor.tokenizers[1])

	worker.queryAbort = false

	worker.externalGroupIndex = executor.externalGroupIndex

	workerEvent := worker.WorkerEvents[eventId]

	workerEvent.storeName = executor.tokenizers[1].Tokens[0]

	workerEvent.keyElementSize = 0 // empty the previous key group

	startIndex := index * 2

	keyElementSize := 0

	for j := executor.keyIndices[startIndex]; j < executor.keyIndices[startIndex+1]; j++ {

		workerEvent.keys[keyElementSize] = executor.keys[j]

		keyElementSize++
	}

	workerEvent.keyElementSize = keyElementSize

	workerEvent.timeWindowQuery = false

	if executor.timeWindowQuery {

		// in event case do not need to set from and to time tick
		utils.Split(workerEvent.storeName, utils.HyphenSeparator, executor.tokenizers[1])

		if store := datastore.GetStore(workerEvent.storeName, utils.None, false, true, executor.encoder, executor.tokenizers[0]); store != nil {

			if executor.tokenizers[1].Tokens[1] == utils.VerticalFormat &&
				(store.GetDatastoreType() == utils.PerformanceMetric || store.GetDatastoreType() == utils.ObjectStatusMetric || store.GetDatastoreType() == utils.NetRouteMetric ||
					store.GetDatastoreType() == utils.NetRouteStatusMetric || store.GetDatastoreType() == utils.TrapFlapHistory || store.GetDatastoreType() == utils.EventPolicy || store.GetDatastoreType() == utils.SLOMetric) {

				baseTick := utils.GetBaseTick(executor.tokenizers[1].Tokens[0])

				workerEvent.timeWindowStartTick = baseTick + executor.timeWindowStartPosition

				workerEvent.timeWindowEndTick = baseTick + executor.timeWindowEndPosition

				workerEvent.timeWindowQuery = true

			}
		}
	}
}

func (executor *Executor) qualifyWorkerEventKeyIndices(start int, store *storage.Store, date time.Time, plugin string, aggregator string) {

	if executor.keyElementSize != start {

		storeName := utils.Empty

		if delta := executor.keyElementSize - start; delta > utils.MaxWorkerEventKeys {

			workerEventKeys := utils.MaxWorkerEventKeys

			remainder := workerEventKeys % executor.probes

			if remainder != 0 {

				workerEventKeys -= remainder
			}

			qualifiedStores := delta / workerEventKeys

			if delta%workerEventKeys > 0 {

				qualifiedStores++
			}

			if executor.qualifiedStoreElementSize+qualifiedStores == len(executor.qualifiedStores) {

				if utils.QueryPlanLogging {

					executor.logQueryPlan(fmt.Sprintf("skipping %v entries, reason: qualified stores limit reached", delta))
				}

				return
			}

			startIndex, endIndex := start, start+workerEventKeys

			for i := 0; i < qualifiedStores; i++ {

				if aggregator != LastFunc && store.GetDatastoreType() == utils.StaticMetric {

					storeName = utils.UnixMillisToDate(date.UnixMilli()) + utils.HyphenSeparator + datastore.VerticalStore + utils.HyphenSeparator + plugin

				} else {

					storeName = store.GetName()
				}

				index := executor.qualifiedStoreElementSize * 2

				executor.keyIndices[index] = startIndex

				executor.keyIndices[index+1] = endIndex

				executor.qualifiedStores[executor.qualifiedStoreElementSize] = storeName

				executor.qualifiedStoreElementSize++

				startIndex = endIndex

				endIndex += workerEventKeys

				if endIndex > delta {

					endIndex = delta
				}
			}

		} else {

			if executor.qualifiedStoreElementSize == len(executor.qualifiedStores) {

				if utils.QueryPlanLogging {

					executor.logQueryPlan(fmt.Sprintf("skipping %v entries, reason: qualified stores limit reached", delta))
				}

				return
			}

			if aggregator != LastFunc && store.GetDatastoreType() == utils.StaticMetric {

				storeName = utils.UnixMillisToDate(date.UnixMilli()) + utils.HyphenSeparator + datastore.VerticalStore + utils.HyphenSeparator + plugin

			} else {

				storeName = store.GetName()
			}

			index := executor.qualifiedStoreElementSize * 2

			executor.keyIndices[index] = start

			executor.keyIndices[index+1] = executor.keyElementSize

			executor.qualifiedStores[executor.qualifiedStoreElementSize] = storeName

			executor.qualifiedStoreElementSize++
		}
	}
}

func (executor *Executor) qualifyDistributionKeys(key, compositeKey string) {

	if compositeKey != utils.Empty {

		if executor.keyElementSize+1 >= len(executor.keys) {

			executor.keys = executor.memoryPool.ExpandStringPool(executor.keyPoolIndex, len(executor.keys)+(executor.poolLength/10))
		}

		executor.keys[executor.keyElementSize] = key

		executor.keyElementSize++

		executor.keys[executor.keyElementSize] = compositeKey

		executor.keyElementSize++
	} else {

		if executor.keyElementSize >= len(executor.keys) {

			executor.keys = executor.memoryPool.ExpandStringPool(executor.keyPoolIndex, len(executor.keys)+(executor.poolLength/10))
		}

		executor.keys[executor.keyElementSize] = key

		executor.keyElementSize++
	}
}
