08-May-2025 02:50:35.620088 PM ERROR [Event Aggregator 21]:error runtime error: index out of range [0] with length 0 occurred while aggregating events for file /home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/datastore-events/1-aggregations/1746696035§demo-testing-event-aggregation-2
08-May-2025 02:50:35.620249 PM ERROR [Event Aggregator 21]:!!!STACK TRACE for event aggregator 21!!! 
 goroutine 352 [running]:
motadatadatastore/datastore/writer.(*EventAggregator).aggregate.func1({0xc04472e5d0, 0x2c})
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:409 +0x125
panic({0xa87200?, 0xc00001aab0?})
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/runtime/panic.go:787 +0x132
motadatadatastore/datastore/writer.(*EventAggregator).aggregate(0xc041670388, 0x5?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:428 +0x27ef
motadatadatastore/datastore/writer.TestAggregateV1(0xc04162ce00)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator_test.go:204 +0x2ae
testing.tRunner(0xc04162ce00, 0xb48c18)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

