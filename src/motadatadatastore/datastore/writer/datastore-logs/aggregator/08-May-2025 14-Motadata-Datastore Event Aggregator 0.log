08-May-2025 02:50:34.003237 PM ERROR [Event Aggregator 0]:error invalid argument occurred while mapping annonymous buffer for event aggregator 0 and index 0
08-May-2025 02:50:34.003408 PM ERROR [Event Aggregator 0]:error invalid argument occurred while mapping annonymous buffer for event aggregator 0 and index 1
08-May-2025 02:50:34.003439 PM ERROR [Event Aggregator 0]:error invalid argument occurred while mapping annonymous buffer for event aggregator 0 and index 2
08-May-2025 02:50:34.003457 PM ERROR [Event Aggregator 0]:error invalid argument occurred while mapping annonymous buffer for event aggregator 0 and index 3
08-May-2025 02:50:34.003473 PM ERROR [Event Aggregator 0]:error invalid argument occurred while mapping annonymous buffer for event aggregator 0 and index 4
08-May-2025 02:50:34.003493 PM ERROR [Event Aggregator 0]:error invalid argument occurred while mapping annonymous buffer for event aggregator 0 and index 5
08-May-2025 02:50:34.003512 PM ERROR [Event Aggregator 0]:error invalid argument occurred while mapping annonymous buffer for event aggregator 0 and index 6
08-May-2025 02:50:34.003529 PM ERROR [Event Aggregator 0]:error invalid argument occurred while mapping annonymous buffer for event aggregator 0 and index 7
08-May-2025 02:50:34.003546 PM ERROR [Event Aggregator 0]:error invalid argument occurred while mapping annonymous txn buffer for event aggregator 0
08-May-2025 02:50:34.003563 PM ERROR [Event Aggregator 0]:error invalid argument occurred while mapping annonymous buffer bytes for event aggregator 0
08-May-2025 02:50:34.503889 PM ERROR [Event Aggregator 0]:error runtime error: index out of range [0] with length 0 occurred while aggregating events for file /home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/datastore-events/1-aggregations/dummy
08-May-2025 02:50:34.503998 PM ERROR [Event Aggregator 0]:!!!STACK TRACE for event aggregator 0!!! 
 goroutine 351 [running]:
motadatadatastore/datastore/writer.(*EventAggregator).aggregate.func1({0xab205a, 0x5})
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:409 +0x125
panic({0xa87200?, 0xc040d48000?})
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/runtime/panic.go:787 +0x132
motadatadatastore/datastore/writer.(*EventAggregator).aggregate(0xc041670008, 0xc040c21f00?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:428 +0x27ef
motadatadatastore/datastore/writer.(*EventAggregator).process(0xc041670008)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:364 +0xe8
motadatadatastore/datastore/writer.(*EventAggregator).Start.func1()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:332 +0x37
created by motadatadatastore/datastore/writer.(*EventAggregator).Start in goroutine 350
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:321 +0x73d

08-May-2025 02:50:34.504117 PM ERROR [Event Aggregator 0]:error runtime error: invalid memory address or nil pointer dereference occurred in event aggregator 0
08-May-2025 02:50:34.504163 PM ERROR [Event Aggregator 0]:!!!STACK TRACE for event aggregator 0!!! 
 goroutine 351 [running]:
motadatadatastore/datastore/writer.(*EventAggregator).process.func1()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:350 +0xec
panic({0xa43de0?, 0xe9def0?})
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/runtime/panic.go:787 +0x132
github.com/dolthub/swiss.(*Map[...]).Clear(...)
	/home/<USER>/go/pkg/mod/github.com/dolthub/swiss@v0.2.1/map.go:239
motadatadatastore/datastore/writer.(*EventAggregator).cleanup(0xc041670008)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:2713 +0x17b
motadatadatastore/datastore/writer.(*EventAggregator).aggregate.func1({0xab205a, 0x5})
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:420 +0x21b
panic({0xa87200?, 0xc040d48000?})
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/runtime/panic.go:787 +0x132
motadatadatastore/datastore/writer.(*EventAggregator).aggregate(0xc041670008, 0xc040c21f00?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:428 +0x27ef
motadatadatastore/datastore/writer.(*EventAggregator).process(0xc041670008)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:364 +0xe8
motadatadatastore/datastore/writer.(*EventAggregator).Start.func1()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:332 +0x37
created by motadatadatastore/datastore/writer.(*EventAggregator).Start in goroutine 350
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:321 +0x73d

08-May-2025 02:50:34.504193 PM ERROR [Event Aggregator 0]:event aggregator 0 restarted
08-May-2025 02:50:35.004266 PM WARN [Event Aggregator 0]:failed to unmap anonymous mapped buffer,reason: invalid argument
08-May-2025 02:50:35.004423 PM WARN [Event Aggregator 0]:failed to unmap anonymous mapped buffer,reason: invalid argument
08-May-2025 02:50:35.004451 PM WARN [Event Aggregator 0]:failed to unmap anonymous mapped buffer,reason: invalid argument
08-May-2025 02:50:35.004471 PM WARN [Event Aggregator 0]:failed to unmap anonymous mapped buffer,reason: invalid argument
08-May-2025 02:50:35.004491 PM WARN [Event Aggregator 0]:failed to unmap anonymous mapped buffer,reason: invalid argument
08-May-2025 02:50:35.004510 PM WARN [Event Aggregator 0]:failed to unmap anonymous mapped buffer,reason: invalid argument
08-May-2025 02:50:35.004529 PM WARN [Event Aggregator 0]:failed to unmap anonymous mapped buffer,reason: invalid argument
08-May-2025 02:50:35.004559 PM WARN [Event Aggregator 0]:failed to unmap anonymous mapped buffer,reason: invalid argument
08-May-2025 02:50:35.004584 PM INFO [Event Aggregator 0]:shutting down event aggregator 0
08-May-2025 02:50:35.519465 PM TRACE [Event Aggregator 0]:aggregator 0 started reading file /home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/datastore-events/1-aggregations/1746694800§500000-flow@@@0§14§30§0§1746696035516019644
08-May-2025 02:50:35.520161 PM TRACE [Event Aggregator 0]:aggregator 0 deleting file /home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/datastore-events/1-aggregations/1746694800§500000-flow@@@0§14§30§0§1746696035516019644
08-May-2025 02:50:35.525809 PM ERROR [Event Aggregator 0]:error runtime error: slice bounds out of range [:2] with capacity 0 occurred while merging groups for store 08052025-1-500000-flow@@@0-5
08-May-2025 02:50:35.525913 PM ERROR [Event Aggregator 0]:!!!STACK TRACE for event aggregator 0!!! 
 goroutine 170 [running]:
motadatadatastore/datastore/writer.(*EventAggregator).merge.func1()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:690 +0x111
panic({0xa87200?, 0xc04234f788?})
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/runtime/panic.go:787 +0x132
motadatadatastore/datastore/writer.(*EventAggregator).merge(0xc041eda008, 0xc040d6e300)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:752 +0x35df
motadatadatastore/datastore/writer.(*EventAggregator).aggregate(0xc041eda008, 0xc0000dfb58?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:569 +0x105a
motadatadatastore/datastore/writer.TestEventAggregatorV2(0xc040d28540?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator_test.go:135 +0x6aa
testing.tRunner(0xc040d28540, 0xb48c90)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

08-May-2025 02:50:35.525998 PM WARN [Event Aggregator 0]:4000 groups merge probing send to offline job for plugin 500000-flow@@@0, tick 168858000 and interval 5
08-May-2025 02:50:35.528525 PM ERROR [Event Aggregator 0]:error runtime error: slice bounds out of range [:2] with capacity 0 occurred while merging groups for store 08052025-1-500000-flow@@@0-30
08-May-2025 02:50:35.528638 PM ERROR [Event Aggregator 0]:!!!STACK TRACE for event aggregator 0!!! 
 goroutine 170 [running]:
motadatadatastore/datastore/writer.(*EventAggregator).merge.func1()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:690 +0x111
panic({0xa87200?, 0xc04234f8f0?})
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/runtime/panic.go:787 +0x132
motadatadatastore/datastore/writer.(*EventAggregator).merge(0xc041eda008, 0xc040d6e480)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:752 +0x35df
motadatadatastore/datastore/writer.(*EventAggregator).aggregate(0xc041eda008, 0xc0000dfb58?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:569 +0x105a
motadatadatastore/datastore/writer.TestEventAggregatorV2(0xc040d28540?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator_test.go:135 +0x6aa
testing.tRunner(0xc040d28540, 0xb48c90)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

08-May-2025 02:50:35.528714 PM WARN [Event Aggregator 0]:4000 groups merge probing send to offline job for plugin 500000-flow@@@0, tick 168858000 and interval 30
08-May-2025 02:50:35.534542 PM ERROR [Event Aggregator 0]:error runtime error: slice bounds out of range [:2] with capacity 0 occurred while merging groups for store 08052025-1-500000-flow@@@0-360
08-May-2025 02:50:35.534681 PM ERROR [Event Aggregator 0]:!!!STACK TRACE for event aggregator 0!!! 
 goroutine 170 [running]:
motadatadatastore/datastore/writer.(*EventAggregator).merge.func1()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:690 +0x111
panic({0xa87200?, 0xc04234fa58?})
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/runtime/panic.go:787 +0x132
motadatadatastore/datastore/writer.(*EventAggregator).merge(0xc041eda008, 0xc040d6e600)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:752 +0x35df
motadatadatastore/datastore/writer.(*EventAggregator).aggregate(0xc041eda008, 0xc0000dfb58?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:569 +0x105a
motadatadatastore/datastore/writer.TestEventAggregatorV2(0xc040d28540?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator_test.go:135 +0x6aa
testing.tRunner(0xc040d28540, 0xb48c90)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

08-May-2025 02:50:35.534769 PM WARN [Event Aggregator 0]:4000 groups merge probing send to offline job for plugin 500000-flow@@@0, tick 168847200 and interval 360
08-May-2025 02:50:35.544345 PM TRACE [Event Aggregator 0]:aggregator 0 started reading file /home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/datastore-events/1-aggregations/1746694800§500000-flow@@@0§13§30§0§1746696035541271085
08-May-2025 02:50:35.544816 PM TRACE [Event Aggregator 0]:aggregator 0 deleting file /home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/datastore-events/1-aggregations/1746694800§500000-flow@@@0§13§30§0§1746696035541271085
08-May-2025 02:50:35.546728 PM ERROR [Event Aggregator 0]:error runtime error: slice bounds out of range [:2] with capacity 0 occurred while merging groups for store 08052025-1-500000-flow@@@0-5
08-May-2025 02:50:35.546881 PM ERROR [Event Aggregator 0]:!!!STACK TRACE for event aggregator 0!!! 
 goroutine 170 [running]:
motadatadatastore/datastore/writer.(*EventAggregator).merge.func1()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:690 +0x111
panic({0xa87200?, 0xc042ce8fd8?})
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/runtime/panic.go:787 +0x132
motadatadatastore/datastore/writer.(*EventAggregator).merge(0xc041eda008, 0xc040d6e300)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:752 +0x35df
motadatadatastore/datastore/writer.(*EventAggregator).aggregate(0xc041eda008, 0xc0000dfa20?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:569 +0x105a
motadatadatastore/datastore/writer.TestEventAggregatorV2(0xc040d28540?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator_test.go:143 +0x86a
testing.tRunner(0xc040d28540, 0xb48c90)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

08-May-2025 02:50:35.546972 PM WARN [Event Aggregator 0]:4000 groups merge probing send to offline job for plugin 500000-flow@@@0, tick 168858000 and interval 5
08-May-2025 02:50:35.549095 PM ERROR [Event Aggregator 0]:error runtime error: slice bounds out of range [:2] with capacity 0 occurred while merging groups for store 08052025-1-500000-flow@@@0-30
08-May-2025 02:50:35.549213 PM ERROR [Event Aggregator 0]:!!!STACK TRACE for event aggregator 0!!! 
 goroutine 170 [running]:
motadatadatastore/datastore/writer.(*EventAggregator).merge.func1()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:690 +0x111
panic({0xa87200?, 0xc042ce9038?})
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/runtime/panic.go:787 +0x132
motadatadatastore/datastore/writer.(*EventAggregator).merge(0xc041eda008, 0xc040d6e480)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:752 +0x35df
motadatadatastore/datastore/writer.(*EventAggregator).aggregate(0xc041eda008, 0xc0000dfa20?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:569 +0x105a
motadatadatastore/datastore/writer.TestEventAggregatorV2(0xc040d28540?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator_test.go:143 +0x86a
testing.tRunner(0xc040d28540, 0xb48c90)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

08-May-2025 02:50:35.549302 PM WARN [Event Aggregator 0]:4000 groups merge probing send to offline job for plugin 500000-flow@@@0, tick 168858000 and interval 30
08-May-2025 02:50:35.551134 PM ERROR [Event Aggregator 0]:error runtime error: slice bounds out of range [:2] with capacity 0 occurred while merging groups for store 08052025-1-500000-flow@@@0-360
08-May-2025 02:50:35.551253 PM ERROR [Event Aggregator 0]:!!!STACK TRACE for event aggregator 0!!! 
 goroutine 170 [running]:
motadatadatastore/datastore/writer.(*EventAggregator).merge.func1()
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:690 +0x111
panic({0xa87200?, 0xc042ce9098?})
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/runtime/panic.go:787 +0x132
motadatadatastore/datastore/writer.(*EventAggregator).merge(0xc041eda008, 0xc040d6e600)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:752 +0x35df
motadatadatastore/datastore/writer.(*EventAggregator).aggregate(0xc041eda008, 0xc0000dfa20?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:569 +0x105a
motadatadatastore/datastore/writer.TestEventAggregatorV2(0xc040d28540?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator_test.go:143 +0x86a
testing.tRunner(0xc040d28540, 0xb48c90)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

08-May-2025 02:50:35.551361 PM WARN [Event Aggregator 0]:4000 groups merge probing send to offline job for plugin 500000-flow@@@0, tick 168847200 and interval 360
08-May-2025 02:50:35.561793 PM TRACE [Event Aggregator 0]:aggregator 0 started reading file /home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/datastore-events/1-aggregations/1746694800§500000-flow@@@0§13§30§0§1746696035557954280
08-May-2025 02:50:35.562273 PM TRACE [Event Aggregator 0]:aggregator 0 deleting file /home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/datastore-events/1-aggregations/1746694800§500000-flow@@@0§13§30§0§1746696035557954280
08-May-2025 02:50:35.568686 PM DEBUG [Event Aggregator 0]:aggregator 0 applied top grouping limit for store 08052025-1-500000-flow@@@0-5 of 10000 from total records of 1000
08-May-2025 02:50:35.569693 PM WARN [Event Aggregator 0]:4000 groups top probing send to offline job for plugin 500000-flow@@@0, tick 168858000 and interval 5
08-May-2025 02:50:35.579719 PM DEBUG [Event Aggregator 0]:aggregator 0 applied top grouping limit for store 08052025-1-500000-flow@@@0-30 of 10000 from total records of 1000
08-May-2025 02:50:35.581500 PM WARN [Event Aggregator 0]:4000 groups top probing send to offline job for plugin 500000-flow@@@0, tick 168858000 and interval 30
08-May-2025 02:50:35.587214 PM DEBUG [Event Aggregator 0]:aggregator 0 applied top grouping limit for store 08052025-1-500000-flow@@@0-360 of 10000 from total records of 1000
08-May-2025 02:50:35.588089 PM WARN [Event Aggregator 0]:4000 groups top probing send to offline job for plugin 500000-flow@@@0, tick 168847200 and interval 360
08-May-2025 02:50:35.594534 PM TRACE [Event Aggregator 0]:aggregator 0 started reading file /home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/datastore-events/1-aggregations/1746694800§500000-trap@@@0§13§30§1§1746696035592299150
08-May-2025 02:50:35.594909 PM TRACE [Event Aggregator 0]:aggregator 0 deleting file /home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/datastore-events/1-aggregations/1746694800§500000-trap@@@0§13§30§1§1746696035592299150
08-May-2025 02:50:35.601918 PM WARN [Event Aggregator 0]:4000 groups append probing send to offline job for plugin 500000-trap@@@0, tick 168858000 and interval 5
08-May-2025 02:50:35.610006 PM WARN [Event Aggregator 0]:4000 groups append probing send to offline job for plugin 500000-trap@@@0, tick 168858000 and interval 30
08-May-2025 02:50:35.617794 PM WARN [Event Aggregator 0]:4000 groups append probing send to offline job for plugin 500000-trap@@@0, tick 168847200 and interval 360
08-May-2025 02:50:35.624823 PM TRACE [Event Aggregator 0]:aggregator 0 started reading file /home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/datastore-events/1-aggregations/1746705600§101-dummy.plugin@@@0§11§5§1§1746696035620950716
08-May-2025 02:50:35.624942 PM TRACE [Event Aggregator 0]:aggregator 0 deleting file /home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/datastore-events/1-aggregations/1746705600§101-dummy.plugin@@@0§11§5§1§1746696035620950716
08-May-2025 02:50:35.640073 PM WARN [Event Aggregator 0]:6 groups merge probing send to offline job for plugin 101-dummy.plugin@@@0, tick 168868800 and interval 360
08-May-2025 02:50:35.640635 PM INFO [Event Aggregator 0]:skipping 101-dummy.plugin@@@0^1746662400^5^1 key 1746706200 tick record as it is being aggregated by the offline job
08-May-2025 02:50:35.640679 PM INFO [Event Aggregator 0]:skipping 101-dummy.plugin@@@0^1746662400^5^1 key 1746705600 tick record as it is being aggregated by the offline job
08-May-2025 02:50:35.640706 PM INFO [Event Aggregator 0]:skipping 101-dummy.plugin@@@0^1746662400^5^1 key 1746705600 tick record as it is being aggregated by the offline job
08-May-2025 02:50:35.640760 PM ERROR [Event Aggregator 0]:aggregator 0 failed to delete file /home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/datastore-events/1-aggregations/1746706200§101-dummy.plugin@@@0§11§5§1§1746696035640603421, reason: remove /home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/datastore-events/1-aggregations/1746706200§101-dummy.plugin@@@0§11§5§1§1746696035640603421: no such file or directory
