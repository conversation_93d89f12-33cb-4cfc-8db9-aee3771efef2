08-May-2025 02:50:35.569431 PM FATAL [Pool]:INT32 pool -1 not released, reason: INT32 pool leaked
08-May-2025 02:50:35.569614 PM ERROR [Pool]:goroutine 170 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseINT32Pool(0xc040d6e780?, 0xffffffffffffffff)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:589 +0x14e
motadatadatastore/datastore/writer.(*EventAggregator).writeIndexableColumns(0xc041eda008, 0xa109190, 0x0, 0x3e8, 0x0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:2303 +0x1110
motadatadatastore/datastore/writer.(*EventAggregator).top(0xc041eda008, 0xc040d6e300, 0xa109190, 0x0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:2102 +0xa85
motadatadatastore/datastore/writer.(*EventAggregator).aggregate(0xc041eda008, 0xc0000dff30?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:597 +0x145a
motadatadatastore/datastore/writer.TestEventAggregatorV2(0xc040d28540?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator_test.go:157 +0xaeb
testing.tRunner(0xc040d28540, 0xb48c90)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

08-May-2025 02:50:35.581331 PM FATAL [Pool]:INT32 pool -1 not released, reason: INT32 pool leaked
08-May-2025 02:50:35.581434 PM ERROR [Pool]:goroutine 170 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseINT32Pool(0xc040d6e780?, 0xffffffffffffffff)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:589 +0x14e
motadatadatastore/datastore/writer.(*EventAggregator).writeIndexableColumns(0xc041eda008, 0xa109190, 0x0, 0x3e8, 0x0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:2303 +0x1110
motadatadatastore/datastore/writer.(*EventAggregator).top(0xc041eda008, 0xc040d6e480, 0xa109190, 0x0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:2102 +0xa85
motadatadatastore/datastore/writer.(*EventAggregator).aggregate(0xc041eda008, 0xc0000dff30?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:597 +0x145a
motadatadatastore/datastore/writer.TestEventAggregatorV2(0xc040d28540?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator_test.go:157 +0xaeb
testing.tRunner(0xc040d28540, 0xb48c90)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

08-May-2025 02:50:35.587892 PM FATAL [Pool]:INT32 pool -1 not released, reason: INT32 pool leaked
08-May-2025 02:50:35.588004 PM ERROR [Pool]:goroutine 170 [running]:
motadatadatastore/utils.(*MemoryPool).ReleaseINT32Pool(0xc040d6e780?, 0xffffffffffffffff)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/utils/pool.go:589 +0x14e
motadatadatastore/datastore/writer.(*EventAggregator).writeIndexableColumns(0xc041eda008, 0xa106760, 0x0, 0x3e8, 0x0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:2303 +0x1110
motadatadatastore/datastore/writer.(*EventAggregator).top(0xc041eda008, 0xc040d6e600, 0xa106760, 0x0)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:2102 +0xa85
motadatadatastore/datastore/writer.(*EventAggregator).aggregate(0xc041eda008, 0xc0000dff30?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator.go:597 +0x145a
motadatadatastore/datastore/writer.TestEventAggregatorV2(0xc040d28540?)
	/home/<USER>/GolandProjects/MotadataDB/src/motadatadatastore/datastore/writer/eventaggregator_test.go:157 +0xaeb
testing.tRunner(0xc040d28540, 0xb48c90)
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/home/<USER>/go/pkg/mod/golang.org/<EMAIL>-amd64/src/testing/testing.go:1851 +0x413

