{"visualization.timeline": {"relative.timeline": "-24h", "visualization.timezone": "Asia/Calcutta", "from.datetime": 1659855720000, "to.datetime": 1659942120000, "duration": 86399}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": []}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}, "id": -1, "ui.event.uuid": "243e0f34-f5db-40b2-ad7e-2850bb8decd2", "session-id": "e04a5a90-62cd-4723-bcce-46a55f29798a", "user.name": "admin", "visualization.data.sources": {"type": "metric", "filters": {"data.filter": {"filter": "include", "groups": [{"filter": "include", "conditions": [{"operand": "interface~alias", "operator": "in", "value": ["loopback0", "loopback1", "loopback2"]}, {"operand": "interface~status", "operator": "=", "value": "up"}], "operator": "or"}], "operator": "and"}, "result.filter": {}}, "visualization.result.by": ["group"], "group": {"server": [1, 2, 3, 4, 5], "network": [6, 7, 8, 9, 10]}, "data.points": [{"data.point": "interface~error.packets", "aggregator": "min", "entity.type": "all", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface", "6": "2000-interface", "7": "2000-interface", "8": "2000-interface", "9": "2000-interface", "10": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.traffic.utilization", "aggregator": "max", "entity.type": "all", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface", "6": "2000-interface", "7": "2000-interface", "8": "2000-interface", "9": "2000-interface", "10": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~out.traffic.utilization", "aggregator": "sum", "entity.type": "all", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface", "6": "2000-interface", "7": "2000-interface", "8": "2000-interface", "9": "2000-interface", "10": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets", "aggregator": "count", "entity.type": "all", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface", "6": "2000-interface", "7": "2000-interface", "8": "2000-interface", "9": "2000-interface", "10": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.traffic.utilization.percent", "aggregator": "min", "entity.type": "all", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface", "6": "2000-interface", "7": "2000-interface", "8": "2000-interface", "9": "2000-interface", "10": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}], "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface", "6": "2000-interface", "7": "2000-interface", "8": "2000-interface", "9": "2000-interface", "10": "2000-interface"}, "status": [], "instance.type": "", "plugins": ["2000-interface"]}, "admin.role": "yes", "query.id": 56278467306504, "sub.query.id": 56278467306505}