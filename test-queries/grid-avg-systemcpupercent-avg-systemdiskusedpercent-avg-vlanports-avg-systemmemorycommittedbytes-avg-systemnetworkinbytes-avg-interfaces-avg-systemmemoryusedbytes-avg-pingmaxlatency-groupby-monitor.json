{"visualization.timeline": {"relative.timeline": "today", "visualization.timezone": "Asia/Calcutta", "from.datetime": 1659119400372, "to.datetime": 1659205799372, "duration": 86399}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": []}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}, "id": -1, "ui.event.uuid": "bc492740-8ac9-45b4-8923-a43e536f3a2d", "session-id": "8ad73c55-9561-4f08-aef7-ca53f86ba675", "user.name": "admin", "query.id": 2110467927701, "visualization.data.sources": {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "system.cpu.percent", "aggregator": "avg", "entity.type": "all", "entities": {"1": "1000-linux", "2": "1000-linux", "3": "1000-linux", "4": "1000-linux", "5": "1000-linux", "6": "1000-linux", "7": "1000-linux", "8": "1000-linux", "9": "1000-linux", "10": "1000-linux"}, "entity.keys": {"1^system.cpu.percent": "1000-linux", "2^system.cpu.percent": "1000-linux", "3^system.cpu.percent": "1000-linux", "4^system.cpu.percent": "1000-linux", "5^system.cpu.percent": "1000-linux", "6^system.cpu.percent": "1000-linux", "7^system.cpu.percent": "1000-linux", "8^system.cpu.percent": "1000-linux", "9^system.cpu.percent": "1000-linux", "10^system.cpu.percent": "1000-linux"}, "plugins": ["1000-linux"]}, {"data.point": "system.disk.used.percent", "aggregator": "avg", "entity.type": "all", "entities": {"1": "1000-linux", "2": "1000-linux", "3": "1000-linux", "4": "1000-linux", "5": "1000-linux", "6": "1000-linux", "7": "1000-linux", "8": "1000-linux", "9": "1000-linux", "10": "1000-linux"}, "entity.keys": {"1^system.disk.used.percent": "1000-linux", "2^system.disk.used.percent": "1000-linux", "3^system.disk.used.percent": "1000-linux", "4^system.disk.used.percent": "1000-linux", "5^system.disk.used.percent": "1000-linux", "6^system.disk.used.percent": "1000-linux", "7^system.disk.used.percent": "1000-linux", "8^system.disk.used.percent": "1000-linux", "9^system.disk.used.percent": "1000-linux", "10^system.disk.used.percent": "1000-linux"}, "plugins": ["1000-linux"]}, {"data.point": "vlan.ports", "aggregator": "avg", "entity.type": "all", "entities": {"1": "1000-linux", "2": "1000-linux", "3": "1000-linux", "4": "1000-linux", "5": "1000-linux", "6": "1000-linux", "7": "1000-linux", "8": "1000-linux", "9": "1000-linux", "10": "1000-linux"}, "entity.keys": {"1^vlan.ports": "1000-linux", "2^vlan.ports": "1000-linux", "3^vlan.ports": "1000-linux", "4^vlan.ports": "1000-linux", "5^vlan.ports": "1000-linux", "6^vlan.ports": "1000-linux", "7^vlan.ports": "1000-linux", "8^vlan.ports": "1000-linux", "9^vlan.ports": "1000-linux", "10^vlan.ports": "1000-linux"}, "plugins": ["1000-linux"]}, {"data.point": "system.memory.committed.bytes", "aggregator": "avg", "entity.type": "all", "entities": {"1": "1000-linux", "2": "1000-linux", "3": "1000-linux", "4": "1000-linux", "5": "1000-linux", "6": "1000-linux", "7": "1000-linux", "8": "1000-linux", "9": "1000-linux", "10": "1000-linux"}, "entity.keys": {"1^system.memory.committed.bytes": "1000-linux", "2^system.memory.committed.bytes": "1000-linux", "3^system.memory.committed.bytes": "1000-linux", "4^system.memory.committed.bytes": "1000-linux", "5^system.memory.committed.bytes": "1000-linux", "6^system.memory.committed.bytes": "1000-linux", "7^system.memory.committed.bytes": "1000-linux", "8^system.memory.committed.bytes": "1000-linux", "9^system.memory.committed.bytes": "1000-linux", "10^system.memory.committed.bytes": "1000-linux"}, "plugins": ["1000-linux"]}, {"data.point": "system.network.in.bytes", "aggregator": "avg", "entity.type": "all", "entities": {"1": "1000-linux", "2": "1000-linux", "3": "1000-linux", "4": "1000-linux", "5": "1000-linux", "6": "1000-linux", "7": "1000-linux", "8": "1000-linux", "9": "1000-linux", "10": "1000-linux"}, "entity.keys": {"1^system.network.in.bytes": "1000-linux", "2^system.network.in.bytes": "1000-linux", "3^system.network.in.bytes": "1000-linux", "4^system.network.in.bytes": "1000-linux", "5^system.network.in.bytes": "1000-linux", "6^system.network.in.bytes": "1000-linux", "7^system.network.in.bytes": "1000-linux", "8^system.network.in.bytes": "1000-linux", "9^system.network.in.bytes": "1000-linux", "10^system.network.in.bytes": "1000-linux"}, "plugins": ["1000-linux"]}, {"data.point": "interfaces", "aggregator": "avg", "entity.type": "all", "entities": {"1": "1000-linux", "2": "1000-linux", "3": "1000-linux", "4": "1000-linux", "5": "1000-linux", "6": "1000-linux", "7": "1000-linux", "8": "1000-linux", "9": "1000-linux", "10": "1000-linux"}, "entity.keys": {"1^interfaces": "1000-linux", "2^interfaces": "1000-linux", "3^interfaces": "1000-linux", "4^interfaces": "1000-linux", "5^interfaces": "1000-linux", "6^interfaces": "1000-linux", "7^interfaces": "1000-linux", "8^interfaces": "1000-linux", "9^interfaces": "1000-linux", "10^interfaces": "1000-linux"}, "plugins": ["1000-linux"]}, {"data.point": "system.memory.used.bytes", "aggregator": "avg", "entity.type": "all", "entities": {"1": "1000-linux", "2": "1000-linux", "3": "1000-linux", "4": "1000-linux", "5": "1000-linux", "6": "1000-linux", "7": "1000-linux", "8": "1000-linux", "9": "1000-linux", "10": "1000-linux"}, "entity.keys": {"1^system.memory.used.bytes": "1000-linux", "2^system.memory.used.bytes": "1000-linux", "3^system.memory.used.bytes": "1000-linux", "4^system.memory.used.bytes": "1000-linux", "5^system.memory.used.bytes": "1000-linux", "6^system.memory.used.bytes": "1000-linux", "7^system.memory.used.bytes": "1000-linux", "8^system.memory.used.bytes": "1000-linux", "9^system.memory.used.bytes": "1000-linux", "10^system.memory.used.bytes": "1000-linux"}, "plugins": ["1000-linux"]}, {"data.point": "ping.max.latency", "aggregator": "avg", "entity.type": "all", "entities": {"1": "1000-linux", "2": "1000-linux", "3": "1000-linux", "4": "1000-linux", "5": "1000-linux", "6": "1000-linux", "7": "1000-linux", "8": "1000-linux", "9": "1000-linux", "10": "1000-linux"}, "entity.keys": {"1^ping.max.latency": "1000-linux", "2^ping.max.latency": "1000-linux", "3^ping.max.latency": "1000-linux", "4^ping.max.latency": "1000-linux", "5^ping.max.latency": "1000-linux", "6^ping.max.latency": "1000-linux", "7^ping.max.latency": "1000-linux", "8^ping.max.latency": "1000-linux", "9^ping.max.latency": "1000-linux", "10^ping.max.latency": "1000-linux"}, "plugins": ["1000-linux"]}], "plugins": ["1000-linux"], "entities": {"1": "1000-linux", "2": "1000-linux", "3": "1000-linux", "4": "1000-linux", "5": "1000-linux", "6": "1000-linux", "7": "1000-linux", "8": "1000-linux", "9": "1000-linux", "10": "1000-linux"}}, "admin.role": "yes", "sub.query.id": 2110467927702}