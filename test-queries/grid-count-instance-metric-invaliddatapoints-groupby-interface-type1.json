{"visualization.timeline": {"relative.timeline": "today", "visualization.timezone": "Asia/Calcutta", "from.datetime": 1659292200617, "to.datetime": 1659378599617, "duration": 86399}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": []}}, "visualization.result.by": ["interface"], "granularity": {"value": 5, "unit": "m"}, "id": -1, "ui.event.uuid": "7b04220d-ccdf-46da-aea8-f230e85964dc", "session-id": "a0b81062-80a4-47cd-8bd3-4a95d0c83145", "user.name": "admin", "visualization.data.sources": {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor", "interface"], "data.points": [{"data.point": "interface~in.packets", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets1", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets2", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets3", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets4", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets5", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets6", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets7", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets8", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets9", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets10", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets11", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets12", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets13", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets14", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets15", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets16", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets17", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets18", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets19", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets20", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}, {"data.point": "interface~in.packets21", "aggregator": "", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {}, "plugins": ["2000-interface"]}], "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "status": [], "instance.type": "interface", "plugins": ["2000-interface"]}, "admin.role": "yes", "query.id": 74205653047123, "sub.query.id": 74205653047124}