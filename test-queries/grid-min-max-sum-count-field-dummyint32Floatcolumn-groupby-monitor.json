{"visualization.timeline": {"relative.timeline": "today", "visualization.timezone": "Asia/Calcutta", "from.datetime": 1659119400198, "to.datetime": 1659205799198, "duration": 86399}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.properties": {"gauge": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": []}}, "granularity": {"value": 5, "unit": "m"}, "id": -1, "ui.event.uuid": "8d887954-eeab-476e-9093-ac13982f477d", "session-id": "8ad73c55-9561-4f08-aef7-ca53f86ba675", "user.name": "admin", "query.id": 91198171542, "visualization.data.sources": {"type": "metric", "filters": {}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "interface~dummy.int32Float.column1", "aggregator": "min", "entity.type": "monitor", "entities": {"1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "2": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "3": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "4": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "5": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "6": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "7": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "8": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "9": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "10": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "entity.keys": {"1^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "2^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "3^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "4^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "5^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "6^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "7^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "8^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "9^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "10^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "plugins": ["303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"data.point": "interface~dummy.int32Float.column1", "aggregator": "max", "entity.type": "monitor", "entities": {"1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "2": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "3": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "4": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "5": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "6": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "7": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "8": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "9": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "10": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "entity.keys": {"1^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "2^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "3^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "4^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "5^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "6^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "7^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "8^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "9^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "10^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "plugins": ["303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"data.point": "interface~dummy.int32Float.column1", "aggregator": "sum", "entity.type": "monitor", "entities": {"1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "2": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "3": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "4": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "5": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "6": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "7": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "8": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "9": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "10": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "entity.keys": {"1^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "2^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "3^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "4^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "5^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "6^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "7^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "8^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "9^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "10^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "plugins": ["303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"data.point": "interface~dummy.int32Float.column1", "aggregator": "count", "entity.type": "monitor", "entities": {"1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "2": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "3": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "4": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "5": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "6": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "7": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "8": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "9": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "10": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "entity.keys": {"1^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "2^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "3^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "4^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "5^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "6^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "7^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "8^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "9^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "10^x^interface~dummy.int32Float.column1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "plugins": ["303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>"]}], "plugins": ["303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>"], "entities": {"1": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "2": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "3": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "4": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "5": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "6": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "7": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "8": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "9": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>", "10": "303-dummy<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "admin.role": "no", "sub.query.id": 91198171543}