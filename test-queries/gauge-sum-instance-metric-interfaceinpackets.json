{"visualization.timeline": {"relative.timeline": "today", "visualization.timezone": "Asia/Calcutta", "from.datetime": 1659465000155, "to.datetime": 1659551399155, "duration": 86399}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.properties": {"gauge": {"style": {"chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "small", "color.conditions": [{"color": "#f04e3e"}, {"color": "#f58518"}, {"color": "#f5bc18"}]}}}, "granularity": {"value": 5, "unit": "m"}, "id": -1, "ui.event.uuid": "aac66de1-13d9-41e6-8675-174f9a0ae143", "session-id": "e04a5a90-62cd-4723-bcce-46a55f29798a", "user.name": "admin", "visualization.data.sources": {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~in.packets", "aggregator": "sum", "entity.type": "Group", "entities": {"6": "2000-interface", "7": "2000-interface", "8": "2000-interface", "9": "2000-interface", "10": "2000-interface"}, "entity.keys": {"6^x^interface~in.packets": "2000-interface", "7^x^interface~in.packets": "2000-interface", "8^x^interface~in.packets": "2000-interface", "9^x^interface~in.packets": "2000-interface", "10^x^interface~in.packets": "2000-interface", "6^y^interface~in.packets": "2000-interface", "7^y^interface~in.packets": "2000-interface", "8^y^interface~in.packets": "2000-interface", "9^y^interface~in.packets": "2000-interface", "10^y^interface~in.packets": "2000-interface", "6^z^interface~in.packets": "2000-interface", "7^z^interface~in.packets": "2000-interface", "8^z^interface~in.packets": "2000-interface", "9^z^interface~in.packets": "2000-interface", "10^z^interface~in.packets": "2000-interface", "6^w^interface~in.packets": "2000-interface", "7^w^interface~in.packets": "2000-interface", "8^w^interface~in.packets": "2000-interface", "9^w^interface~in.packets": "2000-interface", "10^w^interface~in.packets": "2000-interface", "6^a^interface~in.packets": "2000-interface", "7^a^interface~in.packets": "2000-interface", "8^a^interface~in.packets": "2000-interface", "9^a^interface~in.packets": "2000-interface", "10^a^interface~in.packets": "2000-interface"}, "plugins": ["2000-interface"]}], "entities": {"6": "2000-interface", "7": "2000-interface", "8": "2000-interface", "9": "2000-interface", "10": "2000-interface"}, "status": ["in.packets"], "instance.type": "interface", "plugins": ["2000-interface"]}, "admin.role": "yes", "query.id": 157047436841629, "sub.query.id": 157047436841630}