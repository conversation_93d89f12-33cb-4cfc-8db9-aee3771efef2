{"500000-flow-1": {"indexable.columns": ["event.source"], "type": 12, "volume.bytes": 1}, "500000-flow-2": {"indexable.columns": ["source.if.index"], "type": 12, "volume.bytes": 1}, "500000-flow-3": {"indexable.columns": ["destination.if.index"], "type": 12, "volume.bytes": 1}, "500000-flow-4": {"indexable.columns": ["destination.port"], "type": 12, "volume.bytes": 1}, "500000-flow-5": {"indexable.columns": ["protocol"], "type": 12, "volume.bytes": 1}, "500000-flow-6": {"indexable.columns": ["tcp.flags"], "packets": 1, "type": 12}, "500000-flow-7": {"indexable.columns": ["destination.domain"], "type": 12, "volume.bytes": 1}, "500000-flow-8": {"indexable.columns": ["source.domain"], "type": 12, "volume.bytes": 1}, "500000-flow-9": {"indexable.columns": ["destination.ip", "source.ip", "protocol", "source.port"], "type": 12, "volume.bytes": 1}, "500000-flow-10": {"indexable.columns": ["application"], "type": 12, "volume.bytes": 1}, "500000-flow-11": {"indexable.columns": ["destination.country", "destination.city", "source.city", "source.country"], "type": 12, "volume.bytes": 1}, "500000-flow-12": {"indexable.columns": ["destination.country", "source.country"], "type": 12, "volume.bytes": 1}, "500000-flow-13": {"indexable.columns": ["destination.country", "source.city"], "type": 12, "volume.bytes": 1}, "600005-fortinet.traffic-1": {"fortinet.traffic.event.type": 0, "indexable.columns": ["fortinet.traffic.application", "fortinet.traffic.action"], "type": 11}, "600005-fortinet.traffic-2": {"fortinet.traffic.event.type": 0, "indexable.columns": ["fortinet.traffic.service"], "type": 11}, "600005-fortinet.traffic-3": {"fortinet.traffic.event.type": 0, "indexable.columns": ["fortinet.traffic.source.ip"], "type": 11}, "600005-fortinet.traffic-4": {"fortinet.traffic.event.type": 0, "indexable.columns": ["fortinet.traffic.destination.ip"], "type": 11}, "600005-fortinet.traffic-5": {"fortinet.traffic.event.type": 0, "indexable.columns": ["fortinet.traffic.destination.port"], "type": 11}, "600005-fortinet.traffic-6": {"fortinet.traffic.event.type": 0, "indexable.columns": ["fortinet.traffic.application"], "type": 11}, "600005-fortinet.traffic-7": {"fortinet.traffic.event.type": 0, "indexable.columns": ["fortinet.traffic.source.name"], "type": 11}}