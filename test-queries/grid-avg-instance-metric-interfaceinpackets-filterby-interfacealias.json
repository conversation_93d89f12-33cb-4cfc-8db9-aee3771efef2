{"visualization.timeline": {"relative.timeline": "today", "visualization.timezone": "Asia/Calcutta", "from.datetime": 1659465000442, "to.datetime": 1659551399443, "duration": 86399}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": []}}, "visualization.result.by": ["interface"], "granularity": {"value": 5, "unit": "m"}, "id": -1, "ui.event.uuid": "95701cf5-a859-49c1-9ee6-caaf6a7119e0", "session-id": "1bbdc8e0-7812-4d14-a555-422e5a8e86e4", "user.name": "admin", "visualization.data.sources": {"type": "metric", "filters": {"data.filter": {"operator": "or", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "interface~alias", "operator": "in", "value": ["loopback0", "tunnel1", "loopback1", "tunnel2", "loopback2", "tunnel3", "Gi/0, loopbackGi/0", "Gi/0/loopback", "somethingloop", "loopsomething", "tunnelloop", "looptunnel"]}]}]}, "result.filter": {}}, "visualization.result.by": ["monitor", "interface"], "data.points": [{"data.point": "interface~in.packets", "aggregator": "avg", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface", "6": "2000-interface", "7": "2000-interface", "8": "2000-interface", "9": "2000-interface", "10": "2000-interface"}, "entity.type": "all", "plugins": ["2000-interface"]}], "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface", "6": "2000-interface", "7": "2000-interface", "8": "2000-interface", "9": "2000-interface", "10": "2000-interface"}, "status": [], "instance.type": "interface", "plugins": ["2000-interface"]}, "admin.role": "yes", "query.id": 157047436842476, "sub.query.id": 157047436842477}